package com.geeksec.nta.trafficetl.etl.graph.extractor.dns;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.nta.trafficetl.etl.constant.FieldConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.util.List;
import java.util.Map;

/**
 * 会话边提取器
 * 从网络会话中提取边关系数据，包括IP绑定关系、连接关系和应用关系
 *
 * <AUTHOR> Team
 */
@Slf4j
public class DNSEdgeExtractor extends ProcessFunction<Row, Row> {
    // 成功的AnsType
    private static final List<Integer> successAnsType = List.of(1, 5, 28);
    private static final ObjectMapper mapper = new ObjectMapper();

    /**
     * 从会话信息中生成各类边关系
     *
     * @param value 数据映射
     * @param collector 收集器
     * @throws Exception 如果处理失败
     */
    @Override
    public void processElement(Row value, Context ctx, Collector<Row> collector) throws Exception {
        try {
            DNSEdgeExtractorFactory.getEdgeExtractors().forEach(edgeExtractor -> {
                List<Row> edges = edgeExtractor.extractEdge(value);
                for (Row edge : edges){
                    if (edge != null){
                        ctx.output(edgeExtractor.getOutputTag(), edge);
                    }
                }
            });
            collector.collect(value);
        } catch (Exception e) {
            log.error("DNSEdgeExtractor process error: {}", e.getMessage());
        }
    }

    /**
     * 获取DNS查询的queryType
     * @param value
     * @return
     */
    public static int extractQueryType(Row value){
        try {
            String dnsQueStr = value.getField(FieldConstants.FIELD_DNS_QUERY).toString();
            List<Map<String, Object>> dnsQueMaps = mapper.readValue(dnsQueStr, new TypeReference<List<Map<String, Object>>>() {});
            Integer queryType = Integer.valueOf(dnsQueMaps.get(0).get("type").toString());
            return queryType;
        } catch (JsonProcessingException | NumberFormatException e) {
            log.error("解析DNS查询数据JSON失败", e);
        }
        return -1;
    }

    /**
     * 获取DNS查询的answerType, 默认值为0, 表示失败
     * @param value
     * @return
     */
    public static int extractAnswerType(Row value){
        // 获取获取queryType和answerType
        try {
            String dnsAnsStr = value.getField(FieldConstants.FIELD_DNS_ANSWER).toString();
            List<Map<String, Object>> dnsAnsMaps = mapper.readValue(dnsAnsStr, new TypeReference<List<Map<String, Object>>>() {});
            Integer answerType = 0;
            if (CollectionUtils.isEmpty(dnsAnsMaps)) {
                answerType = 2;
            }
            for (Map<String, Object> dnsAns : dnsAnsMaps) {
                Integer type = Integer.valueOf(dnsAns.get("type").toString());
                if (successAnsType.contains(type)) {
                    answerType = 1;
                    break;
                }
            }
            return answerType;
        } catch (JsonProcessingException | NumberFormatException e) {
            log.error("解析DNS查询数据JSON失败", e);
        }
        return 0;
    }
}
