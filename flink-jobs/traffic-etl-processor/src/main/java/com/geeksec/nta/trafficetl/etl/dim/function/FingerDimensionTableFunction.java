package com.geeksec.nta.trafficetl.etl.dim.function;

//// import com.geeksec.common.utils.knowledgebase.FingerprintKnowledgeManager;

import com.geeksec.nta.trafficetl.etl.constant.FieldConstants;
import com.geeksec.nta.trafficetl.etl.dim.DimensionProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Map;

import static com.geeksec.common.toolkit.time.TimeUtils.DORIS_DATETIME_FORMATTER;

/**
 * 指纹维度表处理函数，用于生成符合dim_sslfinger表结构的维度数据
 * <p>
 * 使用Flink Backend State进行缓存管理，支持TTL自动清理
 *
 * <AUTHOR>
 */
@Slf4j
public class FingerDimensionTableFunction extends ProcessFunction<Row, Row> {
    private static final long serialVersionUID = 1L;
    private final Duration ttl;
    /**
     * 指纹维度数据输出标签
     */
    public static final OutputTag<Row> FINGER_DIM_TAG = new OutputTag<Row>("finger-dimension") {
    };
    public static final OutputTag<Row> FINGER_VERTEX_TAG = new OutputTag<Row>("finger-tag") {
    };
    /**
     * 缓存指纹维度数据的状态
     */
    private transient MapState<String, Map<String, Object>> fingerDimensionState;
    /**
     * 指纹知识库数据
     */
    private transient FingerprintKnowledgeManager fingerprintKnowledgeManager;

    /**
     * 构造函数
     */
    public FingerDimensionTableFunction() {
        // 默认12小时TTL
        this(Duration.ofHours(12));
    }

    /**
     * 构造函数
     *
     * @param ttl 状态TTL时间
     */
    public FingerDimensionTableFunction(Duration ttl) {
        this.ttl = ttl;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 配置状态TTL
        StateTtlConfig ttlConfig = StateTtlConfig.newBuilder(ttl).setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite).setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired).build();

        // 初始化指纹维度状态
        MapStateDescriptor<String, Map<String, Object>> fingerStateDescriptor = new MapStateDescriptor<>("finger-dimension-state",
                TypeInformation.of(new TypeHint<String>() {
                }),
                TypeInformation.of(new TypeHint<Map<String, Object>>() {
                }));
        fingerStateDescriptor.enableTimeToLive(ttlConfig);
        fingerDimensionState = getRuntimeContext().getMapState(fingerStateDescriptor);
        fingerprintKnowledgeManager = FingerprintKnowledgeManager.getInstance();
    }

    @Override
    public void processElement(Row value, Context ctx, Collector<Row> out) {
        try {
            String sSSLFinger = value.getField(FieldConstants.FIELD_SSL_S_FINGER).toString();
            String dSSLFinger = value.getField(FieldConstants.FIELD_SSL_C_FINGER).toString();

            if (!sSSLFinger.isEmpty() && !"0".equals(sSSLFinger)) {
                updateStateMap(sSSLFinger, fingerDimensionState);
                ctx.output(FINGER_DIM_TAG, createFingerDimensionRow(sSSLFinger));
                ctx.output(FINGER_VERTEX_TAG, createFingerVertexRow(sSSLFinger));
            }

            if (!dSSLFinger.isEmpty() && !"0".equals(dSSLFinger)) {
                updateStateMap(dSSLFinger, fingerDimensionState);
                ctx.output(FINGER_VERTEX_TAG, createFingerVertexRow(dSSLFinger));
            }

            out.collect(value);
        } catch (Exception e) {
            log.error("处理指纹维度数据时发生错误: {}", e.getMessage(), e);
            out.collect(value);
        }
    }

    private static void updateStateMap(String fingerprint, MapState<String, Map<String, Object>> fingerDimensionState) {
        // 更新state map状态
        try {
            Map<String, Object> existingAppInfo = fingerDimensionState.get(fingerprint);
            if (existingAppInfo == null) {
                fingerDimensionState.put(fingerprint, Map.of(
                        FieldConstants.KEY_JA3_HASH, fingerprintKnowledgeManager.getFingerJa3(fingerprint),
                        FieldConstants.KEY_FINGERPRINT_TYPE, fingerprintKnowledgeManager.getFingerType(fingerprint),
                        FieldConstants.KEY_FINGERPRINT_DESC, "",
                        FieldConstants.KEY_THREAT_SCORE, 0,
                        FieldConstants.KEY_TRUST_SCORE, 100));
            } else {
                fingerDimensionState.get(fingerprint).put(FieldConstants.KEY_DW_LAST_UPDATED_TIMESTAMP, LocalDateTime.now().format(DORIS_DATETIME_FORMATTER));
            }
        } catch (Exception e) {
            log.error("更新状态数据时发生错误: {}", e.getMessage());
        }
    }

    /**
     * 创建设备维度记录
     *
     * @param finger
     * @return 符合dim_device表结构的Row
     */
    private Row createFingerDimensionRow(String finger) {
        try {
            Map<String, Object> fingerStateMap = fingerDimensionState.get(finger);
            Row dimensionRow = Row.withNames();
            dimensionRow.setField("ja3_hash", fingerStateMap.get(FieldConstants.KEY_JA3_HASH));
            dimensionRow.setField("type", fingerStateMap.get(FieldConstants.KEY_FINGERPRINT_TYPE));
            dimensionRow.setField("finger_desc", fingerStateMap.get(FieldConstants.KEY_FINGERPRINT_DESC));
            dimensionRow.setField("threat_score", fingerStateMap.get(FieldConstants.KEY_THREAT_SCORE));
            dimensionRow.setField("trust_score", fingerStateMap.get(FieldConstants.KEY_TRUST_SCORE));
            DimensionProcessor.insertTimeStateMap(fingerDimensionState, finger, dimensionRow);

            return dimensionRow;
        } catch (Exception e) {
            log.error("创建设备维度记录时发生错误: {}", e.getMessage());
            Row dimensionRow = Row.withNames();
            dimensionRow.setField("ja3_hash", fingerprintKnowledgeManager.getFingerJa3(finger));
            dimensionRow.setField("type", fingerprintKnowledgeManager.getFingerType(finger));
            dimensionRow.setField("finger_desc", "");
            dimensionRow.setField("threat_score", 0);
            dimensionRow.setField("trust_score", 100);
            DimensionProcessor.insertTimeStateMap(fingerDimensionState, finger, dimensionRow);

            return dimensionRow;
        }
    }

    /**
     * 创建指纹TAG记录
     *
     * @param finger
     * @return 符合指纹TAG结构的Row
     */
    private Row createFingerVertexRow(String finger) {
        try {
            Map<String, Object> fingerStateMap = fingerDimensionState.get(finger);

            return Row.of(finger, // vid
                    fingerStateMap.get(FieldConstants.KEY_JA3_HASH), fingerStateMap.get(FieldConstants.KEY_FINGERPRINT_TYPE),
                    fingerStateMap.get(FieldConstants.KEY_FINGERPRINT_DESC),
                    fingerStateMap.get(FieldConstants.KEY_THREAT_SCORE), fingerStateMap.get(FieldConstants.KEY_TRUST_SCORE));
        } catch (Exception e) {
            log.error("创建设备维度记录时发生错误: {}", e.getMessage());
            return Row.of(finger, // vid
                    fingerprintKnowledgeManager.getFingerJa3(finger), fingerprintKnowledgeManager.getFingerType(finger),
                    "", 0, 100
            );
        }
    }
}
