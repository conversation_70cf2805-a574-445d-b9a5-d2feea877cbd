package com.geeksec.nta.trafficetl.etl.dim.function;

import com.geeksec.nta.trafficetl.etl.constant.FieldConstants;
import com.geeksec.nta.trafficetl.etl.dim.DimensionProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import java.time.Duration;
import java.util.Map;
import java.util.Set;

/**
 * MAC维度表处理函数，用于生成符合dim_mac表结构的维度数据
 * <p>
 * 使用Flink Backend State进行缓存管理，支持TTL自动清理
 *
 * <AUTHOR>
 */
@Slf4j
public class MacDimensionTableFunction extends ProcessFunction<Row, Row> {
    private static final long serialVersionUID = 1L;

    /**
     * MAC维度数据输出标签
     */
    public static final OutputTag<Row> MAC_DIM_TAG = new OutputTag<Row>("mac-dimension") {
    };
    public static final OutputTag<Row> MAC_VERTEX_TAG = new OutputTag<Row>("mac-tag") {
    };
    private final String macFieldName;
    private final Duration ttl;
    // State for caching MAC dimension data
    private transient MapState<String, Map<String, Object>> macDimensionState;

    /**
     * 构造函数
     *
     * @param macFieldName MAC地址字段名
     */
    public MacDimensionTableFunction(String macFieldName) {
        this(macFieldName, Duration.ofHours(24)); // 默认24小时TTL
    }

    /**
     * 构造函数
     *
     * @param macFieldName MAC地址字段名
     * @param ttl          状态TTL时间
     */
    public MacDimensionTableFunction(String macFieldName, Duration ttl) {
        this.macFieldName = macFieldName;
        this.ttl = ttl;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 配置状态TTL
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(ttl)
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();

        // 初始化MAC维度状态
        MapStateDescriptor<String, Map<String, Object>> macStateDescriptor =
                new MapStateDescriptor<>("mac-dimension-state",
                        TypeInformation.of(new TypeHint<String>() {
                        }), TypeInformation.of(new TypeHint<Map<String, Object>>() {
                }));
        macStateDescriptor.enableTimeToLive(ttlConfig);
        macDimensionState = getRuntimeContext().getMapState(macStateDescriptor);
    }

    @Override
    public void processElement(Row value, Context ctx, Collector<Row> out) {
        try {
            String macAddress = getStringFieldValue(value, macFieldName);
            if (macAddress == null || macAddress.trim().isEmpty()) {
                out.collect(value);
                return;
            }

            DimensionProcessor.updateTimeStateMap(macDimensionState, macAddress);

            ctx.output(MAC_DIM_TAG, createDimensionRow(macAddress, value));
            ctx.output(MAC_VERTEX_TAG, createVertexRow(macAddress));
            out.collect(value);
        } catch (Exception e) {
            log.error("处理MAC维度数据时发生错误: {}", e.getMessage(), e);
            out.collect(value);
        }
    }

    /**
     * 创建符合维度表结构的MAC维度记录
     *
     * @param macAddress MAC地址
     * @param value
     * @return 符合dim_mac表结构的Row
     */
    private Row createDimensionRow(String macAddress, Row value) {
        Row dimensionRow = Row.withNames();
        dimensionRow.setField("mac", macAddress);
        dimensionRow.setField("vlan_info", null);
        dimensionRow.setField("threat_score", 0);
        dimensionRow.setField("trust_score", 100);
        dimensionRow.setField("remark", null);
        dimensionRow.setField("total_bytes", Long.parseLong(value.getField(FieldConstants.FIELD_PKT_DPAYLOADBYTES).toString()) + Long.parseLong(value.getField(FieldConstants.FIELD_PKT_SPAYLOADBYTES).toString()));
        dimensionRow.setField("session_count", 1L);
        dimensionRow.setField("total_duration", 0L);
        DimensionProcessor.insertTimeStateMap(macDimensionState, macAddress, dimensionRow);

        return dimensionRow;
    }

    /**
     * 创建符合MAC TAG结构的记录
     *
     * @param macAddress MAC地址
     * @return 符合MAC TAG结构的Row
     */
    private Row createVertexRow(String macAddress) {
        return Row.of(macAddress, // vid
                macAddress, "", 0, 100, ""
        );
    }

    /**
     * 从Row中获取字符串字段值
     *
     * @param row       Row对象
     * @param fieldName 字段名
     * @return 字段值，如果不存在或类型不匹配则返回null
     */
    private String getStringFieldValue(Row row, String fieldName) {
        try {
            Set<String> fieldNames = row.getFieldNames(true);
            if (fieldNames != null && fieldNames.contains(fieldName)) {
                Object fieldValue = row.getField(fieldName);
                if (fieldValue instanceof String) {
                    return (String) fieldValue;
                }
            }
        } catch (Exception e) {
            log.warn("获取字符串字段值时发生错误，字段名: {}, 错误: {}", fieldName, e.getMessage());
        }
        return null;
    }
}
