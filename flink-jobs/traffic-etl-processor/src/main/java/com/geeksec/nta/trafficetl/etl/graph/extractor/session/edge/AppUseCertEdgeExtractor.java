package com.geeksec.nta.trafficetl.etl.graph.extractor.session.edge;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.nta.trafficetl.etl.constant.FieldConstants;
import com.geeksec.nta.trafficetl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.trafficetl.sink.tag.NebulaGraphOutputTag;
import com.geeksec.proto.ZMPNMsg;
import com.google.protobuf.ByteString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description null.java
 * @Date 9:30$ 2025/6/18$
 **/
@Slf4j
public class AppUseCertEdgeExtractor extends BaseEdgeExtractor {
    // 默认版本
    public static final String APP_DEFAULT_VERSION = "1.0";
    private static final ObjectMapper mapper = new ObjectMapper();
    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.APP_USES_CERT_TAG;
    }

    /**
     * 应用程序使用证书 (CERT -> APP)
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        List<Row> edges = new ArrayList<>();
        // 获取应用信息
        String appVidKey = "";
        String appName = value.getField(FieldConstants.FIELD_APP_NAME).toString();
        // 过滤不是App应用的会话
        if (appName.contains("_")) {
            return Collections.emptyList();
        } else {
            // 处理App vid
            appVidKey = appName + "_" + APP_DEFAULT_VERSION;
        }
        // 获取证书列表
        try {
            List<String> certIds = new ArrayList<>();
            List<ZMPNMsg.single_ssl> sslList = mapper.readValue(value.getField(FieldConstants.SESSION_SSL_LIST).toString(), new TypeReference<List<ZMPNMsg.single_ssl>>() {});
            for (ZMPNMsg.single_ssl ssl : sslList){
                ByteString sCert = ssl.getSCert();
                List<String> sCertList = mapper.readValue(sCert.toString(), new TypeReference<List<String>>() {});
                certIds.addAll(sCertList);
            }
            // 证书列表去重
            List<String> distinctCertIds = certIds.stream().distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(distinctCertIds)) {
                for (String certId : distinctCertIds) {
                    edges.add(Row.of(certId, appVidKey,
                            0 // rank 暂定0
                    ));
                }
            }
        } catch (JsonProcessingException e) {
            log.error("解析证书列表失败", e.getMessage());
            return Collections.emptyList();
        }
        return edges;
    }
}
