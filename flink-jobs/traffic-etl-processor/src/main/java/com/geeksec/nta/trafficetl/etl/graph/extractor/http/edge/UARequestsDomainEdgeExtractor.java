package com.geeksec.nta.trafficetl.etl.graph.extractor.http.edge;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.common.infrastructure.network.DomainUtils;
import com.geeksec.nta.trafficetl.etl.constant.FieldConstants;
import com.geeksec.nta.trafficetl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.trafficetl.etl.graph.extractor.http.HttpEdgeExtractor;
import com.geeksec.nta.trafficetl.sink.tag.NebulaGraphOutputTag;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description null.java
 * @Date 9:30$ 2025/6/18$
 **/
@Slf4j
public class UARequestsDomainEdgeExtractor extends BaseEdgeExtractor {
    private static final ObjectMapper mapper = new ObjectMapper();

    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.UA_REQUESTS_DOMAIN_TAG;
    }

    /**
     * UA请求域名 (UA -> DOMAIN)
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        try {
            String httpClientStr = value.getField(FieldConstants.FIELD_HTTP_CLIENT_KV).toString();
            Map<String, Object> httpClient = mapper.readValue(httpClientStr, new TypeReference<Map<String, Object>>() {});
            String domainAddr = httpClient.get("Host").toString();

            // 获取UA信息
            String uaKey = HttpEdgeExtractor.extractUAKey(httpClient);
            if (StringUtil.isNullOrEmpty(uaKey) || DomainUtils.isValidDomain(domainAddr)){
                return Collections.emptyList();
            }
            return List.of(Row.of(uaKey, domainAddr,
                    0 // rank 暂定0
            ));
        } catch (JsonProcessingException e) {
            log.error("解析HTTP信息JSON失败", e);
        }
        return Collections.emptyList();
    }
}
