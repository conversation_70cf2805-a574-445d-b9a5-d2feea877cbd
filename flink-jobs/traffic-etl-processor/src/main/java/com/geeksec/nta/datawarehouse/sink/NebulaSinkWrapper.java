package com.geeksec.nta.datawarehouse.sink;


import com.geeksec.common.config.ConfigConstants;
import com.geeksec.nta.datawarehouse.etl.graph.model.EdgeType;
import com.geeksec.nta.datawarehouse.etl.graph.model.VertexTag;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.flink.types.Row;

/**
 * Nebula Sink包装类
 * 提供便捷的方法来创建各种顶点和边的Sink
 *
 * <AUTHOR> Team
 */
@Slf4j
public class NebulaSinkWrapper {

    /**
     * 创建IP顶点写入操作
     *
     * @param stream IP顶点数据流
     */
    public static void createIpVertexSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getVertexSink(VertexTag.IP, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula IP顶点写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建MAC顶点写入操作
     *
     * @param stream MAC顶点数据流
     */
    public static void createMacVertexSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getVertexSink(VertexTag.MAC, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula MAC顶点写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建设备顶点写入操作
     *
     * @param stream 设备顶点数据流
     */
    public static void createDeviceVertexSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getVertexSink(VertexTag.DEVICE, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula 设备顶点写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建应用顶点写入操作
     *
     * @param stream 应用顶点数据流
     */
    public static void createAppVertexSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getVertexSink(VertexTag.APP, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula 应用顶点写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建操作系统顶点写入操作
     *
     * @param stream 操作系统顶点数据流
     */
    public static void createOsVertexSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getVertexSink(VertexTag.OS, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula 操作系统顶点写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建域名顶点写入操作
     *
     * @param stream 域名顶点数据流
     */
    public static void createDomainVertexSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getVertexSink(VertexTag.DOMAIN, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula 域名顶点写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建基础域名顶点写入操作
     *
     * @param stream 基础域名顶点数据流
     */
    public static void createRegistrableDomainVertexSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getVertexSink(VertexTag.REGISTRABLE_DOMAIN, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula 基础域名顶点写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建URL顶点写入操作
     *
     * @param stream URL顶点数据流
     */
    public static void createUrlVertexSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getVertexSink(VertexTag.URL, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula URL顶点写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建证书顶点写入操作
     *
     * @param stream 证书顶点数据流
     */
    public static void createCertificateVertexSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getVertexSink(VertexTag.CERT, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula 证书顶点写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建证书颁发者顶点写入操作
     *
     * @param stream 证书颁发者顶点数据流
     */
    public static void createIssuerVertexSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getVertexSink(VertexTag.ISSUER, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula 证书颁发者顶点写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建证书主体顶点写入操作
     *
     * @param stream 证书主体顶点数据流
     */
    public static void createSubjectVertexSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getVertexSink(VertexTag.SUBJECT, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula 证书主体顶点写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建组织顶点写入操作
     *
     * @param stream 组织顶点数据流
     */
    public static void createOrgVertexSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getVertexSink(VertexTag.ORG, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula 组织顶点写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建SSL指纹顶点写入操作
     *
     * @param stream SSL指纹顶点数据流
     */
    public static void createSslFingerprintVertexSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getVertexSink(VertexTag.SSL_FINGERPRINT, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula SSL指纹顶点写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建用户代理顶点写入操作
     *
     * @param stream 用户代理顶点数据流
     */
    public static void createUaVertexSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getVertexSink(VertexTag.UA, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula 用户代理顶点写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建IP到MAC映射边写入操作
     *
     * @param stream IP到MAC映射边数据流
     */
    public static void createIpMapsToMacEdgeSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getEdgeSink(EdgeType.IP_MAPS_TO_MAC, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula IP到MAC映射边写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建IP连接边写入操作
     *
     * @param stream IP连接边数据流
     */
    public static void createIpConnectsToIpEdgeSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getEdgeSink(EdgeType.IP_CONNECTS_TO_IP, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula IP连接边写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建MAC连接边写入操作
     *
     * @param stream MAC连接边数据流
     */
    public static void createMacConnectsToMacEdgeSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getEdgeSink(EdgeType.MAC_CONNECTS_TO_MAC, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula MAC连接边写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建域名派生自基础域名边写入操作
     *
     * @param stream 域名派生自基础域名边数据流
     */
    public static void createDomainDerivesFromBaseDomainEdgeSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getEdgeSink(EdgeType.DOMAIN_DERIVES_FROM_REGISTRABLE_DOMAIN, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula 域名派生自基础域名边写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建域名解析到IP边写入操作
     *
     * @param stream 域名解析到IP边数据流
     */
    public static void createDomainResolvesToIpEdgeSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getEdgeSink(EdgeType.DOMAIN_RESOLVES_TO_IP, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula 域名解析到IP边写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建客户端HTTP请求域名边写入操作
     *
     * @param stream 客户端HTTP请求域名边数据流
     */
    public static void createClientHttpRequestsDomainEdgeSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getEdgeSink(EdgeType.CLIENT_HTTP_REQUESTS_DOMAIN, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula 客户端HTTP请求域名边写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建服务器HTTP提供域名边写入操作
     *
     * @param stream 服务器HTTP提供域名边数据流
     */
    public static void createServerHttpServesDomainEdgeSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getEdgeSink(EdgeType.SERVER_HTTP_SERVES_DOMAIN, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula 服务器HTTP提供域名边写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建客户端TLS请求域名边写入操作
     *
     * @param stream 客户端TLS请求域名边数据流
     */
    public static void createClientTlsRequestsDomainEdgeSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getEdgeSink(EdgeType.CLIENT_TLS_REQUESTS_DOMAIN, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula 客户端TLS请求域名边写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建服务器TLS托管域名边写入操作
     *
     * @param stream 服务器TLS托管域名边数据流
     */
    public static void createServerTlsHostsDomainEdgeSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getEdgeSink(EdgeType.SERVER_TLS_HOSTS_DOMAIN, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula 服务器TLS托管域名边写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建客户端查询域名边写入操作
     *
     * @param stream 客户端查询域名边数据流
     */
    public static void createClientQueriesDomainEdgeSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getEdgeSink(EdgeType.CLIENT_QUERIES_DOMAIN, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula 客户端查询域名边写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建客户端查询DNS服务器边写入操作
     *
     * @param stream 客户端查询DNS服务器边数据流
     */
    public static void createClientQueriesDnsServerEdgeSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getEdgeSink(EdgeType.CLIENT_QUERIES_DNS_SERVER, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula 客户端查询DNS服务器边写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建DNS服务器解析域名边写入操作
     *
     * @param stream DNS服务器解析域名边数据流
     */
    public static void createDnsServerResolvesDomainEdgeSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getEdgeSink(EdgeType.DNS_SERVER_RESOLVES_DOMAIN, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula DNS服务器解析域名边写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建用户代理请求域名边写入操作
     *
     * @param stream 用户代理请求域名边数据流
     */
    public static void createUaRequestsDomainEdgeSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getEdgeSink(EdgeType.UA_REQUESTS_DOMAIN, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula 用户代理请求域名边写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建客户端使用用户代理边写入操作
     *
     * @param stream 客户端使用用户代理边数据流
     */
    public static void createClientUsesUaEdgeSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getEdgeSink(EdgeType.CLIENT_USES_UA, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula 客户端使用用户代理边写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    // 以下方法已移除，因为相关的边类型在nebula-init.yaml中不存在
    // createCertIssuedByEdgeSink

    /**
     * 创建证书有主体边写入操作
     *
     * @param stream 证书有主体边数据流
     */
    public static void createCertHasSubjectEdgeSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getEdgeSink(EdgeType.CERT_HAS_SUBJECT, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula 证书有主体边写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建攻击者标签顶点写入操作
     *
     * @param stream 攻击者标签顶点数据流
     */
    public static void createAttackerVertexSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getVertexSink(VertexTag.ATTACKER, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula 攻击者标签顶点写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建受害者标签顶点写入操作
     *
     * @param stream 受害者标签顶点数据流
     */
    public static void createVictimVertexSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getVertexSink(VertexTag.VICTIM, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula 受害者标签顶点写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建客户端访问应用边写入操作
     *
     * @param stream 客户端访问应用边数据流
     */
    public static void createClientAccessesAppEdgeSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getEdgeSink(EdgeType.CLIENT_ACCESSES_APP, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula 客户端访问应用边写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建应用部署在服务器边写入操作
     *
     * @param stream 应用部署在服务器边数据流
     */
    public static void createAppDeploysOnServerEdgeSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getEdgeSink(EdgeType.APP_DEPLOYS_ON_SERVER, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula 应用部署在服务器边写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }

    /**
     * 创建TLS指纹标识应用边写入操作
     *
     * @param stream TLS指纹标识应用边数据流
     */
    public static void createFingerprintIdentifiesAppEdgeSink(DataStream<Row> stream, ParameterTool config) {
        if (stream == null) {
            return;
        }

        SinkFunction<Row> sink = NebulaSinkFactory.getEdgeSink(EdgeType.FINGERPRINT_IDENTIFIES_APP, config);
        if (sink != null) {
            stream.addSink(sink)
                    .name("Nebula TLS指纹标识应用边写入")
                    .setParallelism(config.getInt(ConfigConstants.PARALLELISM_NEBULA_SINK, 1));
        }
    }
}