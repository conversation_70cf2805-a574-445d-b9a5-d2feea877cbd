package com.geeksec.nta.trafficetl.job;

import java.util.Collections;
import java.util.Properties;

import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.types.Row;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;

import com.geeksec.common.config.ConfigurationManager;
import com.geeksec.common.core.constants.ConfigConstants;
import com.geeksec.common.sink.FlinkDorisSinkManager;
import com.geeksec.nta.trafficetl.common.MessageType;
import com.geeksec.nta.trafficetl.etl.dim.DimensionProcessor;
import com.geeksec.nta.trafficetl.etl.dwd.model.StreamData;
import com.geeksec.nta.trafficetl.etl.dwd.processor.DwdSessionProcessor;
import com.geeksec.nta.trafficetl.etl.ods.processor.CertificateExtractor;
import com.geeksec.nta.trafficetl.etl.ods.processor.MessageProcessor;
import com.geeksec.nta.trafficetl.etl.ods.tag.MessageOutputTag;
import com.geeksec.nta.trafficetl.sink.KafkaSinkManager;
import com.geeksec.nta.trafficetl.source.ProtobufTrafficMetadataDeserializer;
import com.geeksec.proto.ZMPNMsg;

import lombok.extern.slf4j.Slf4j;

/**
 * 流量ETL处理流水线
 * 从Kafka读取网络流量数据，进行ETL处理和分析，并将结果写入Doris
 *
 * <AUTHOR>
 */
@Slf4j
public class TrafficEtlPipeline {
        /**
         * 全局配置参数
         */
        private static final ParameterTool CONFIG = ConfigurationManager.getConfig();

        /**
         * Kafka 源并行度
         */
        public static final int PARALLELISM_KAFKA_SOURCE_VALUE = CONFIG
                        .getInt(ConfigConstants.PARALLELISM_KAFKA_SOURCE);

        /**
         * 解析操作并行度
         */
        public static final int PARALLELISM_PARSING_VALUE = CONFIG.getInt(ConfigConstants.PARALLELISM_PARSING);

        /**
         * 配置指定消息类型的 Doris Sink
         *
         * @param stream  输入数据流
         * @param config  配置参数
         * @param msgType 消息类型
         */
        private static void configureDorisSink(DataStream<Row> stream, ParameterTool config, MessageType msgType) {
                if (stream == null) {
                        log.warn("{} 数据流为 null，跳过配置 Doris Sink", msgType);
                        return;
                }

                // 获取表名，根据消息类型映射到对应的表名
                String tableName = "ods_" + msgType.name().toLowerCase();

                // 获取并行度配置，默认为4
                int parallelism = config.getInt("doris.sink.parallelism", 4);

                // 使用 DorisConnectionManager 创建 Sink
                stream.sinkTo(FlinkDorisSinkManager.buildDorisSink(tableName, config))
                                .name(tableName + " Doris Sink")
                                .setParallelism(parallelism);
        }

        /**
         * 主方法，启动网络流量处理流水线
         *
         * @param args 命令行参数
         * @throws Exception 如果执行过程中发生错误
         */
        public static void main(String[] args) throws Exception {
                // 将配置注册到全局
                StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
                env.getConfig().setGlobalJobParameters(CONFIG);

                // 设置链式任务
                env.disableOperatorChaining();

                log.info("Connecting to Kafka brokers: {}, group: {}",
                                CONFIG.get(ConfigConstants.KAFKA_BOOTSTRAP_SERVERS),
                                CONFIG.get(ConfigConstants.KAFKA_GROUP_ID));

                // 1.开始读取Kafka PB数据流至DataStream
                DataStream<ZMPNMsg.JKNmsg> rawStream = createKafkaSource(env);

                // 证书提取处理（如果启用）
                SingleOutputStreamOperator<ZMPNMsg.JKNmsg> certificateExtractedStream = rawStream;
                if (CONFIG.getBoolean(ConfigConstants.CERTIFICATE_EXTRACTION_ENABLED, false)) {
                        log.info("启用证书提取功能");
                        certificateExtractedStream = rawStream
                                        .process(new CertificateExtractor())
                                        .setParallelism(CONFIG
                                                        .getInt(ConfigConstants.CERTIFICATE_EXTRACTION_PARALLELISM, 2))
                                        .name("Certificate Extraction");

                        // 获取证书数据流并发送到Kafka
                        DataStream<byte[]> certificateStream = certificateExtractedStream
                                        .getSideOutput(CertificateExtractor.CERTIFICATE_OUTPUT_TAG);

                        // 发送证书数据到Kafka
                        KafkaSinkManager.configureCertificateKafkaSink(certificateStream, CONFIG);
                }

                // 转化pb数据并直接路由到侧输出流
                SingleOutputStreamOperator<Row> mainStream = certificateExtractedStream
                                .process(new MessageProcessor())
                                .setParallelism(PARALLELISM_PARSING_VALUE)
                                .name("Protocol Message Processing");

                // 获取侧输出流
                DataStream<Row> dnsStream = mainStream.getSideOutput(MessageOutputTag.DNS_STREAM);
                DataStream<Row> httpStream = mainStream.getSideOutput(MessageOutputTag.HTTP_STREAM);
                DataStream<Row> sslStream = mainStream.getSideOutput(MessageOutputTag.SSL_STREAM);
                DataStream<Row> sessionStream = mainStream.getSideOutput(MessageOutputTag.SESSION_STREAM);
                DataStream<Row> sshStream = mainStream.getSideOutput(MessageOutputTag.SSH_STREAM);
                DataStream<Row> rloginStream = mainStream.getSideOutput(MessageOutputTag.RLOGIN_STREAM);
                DataStream<Row> telnetStream = mainStream.getSideOutput(MessageOutputTag.TELNET_STREAM);
                DataStream<Row> rdpStream = mainStream.getSideOutput(MessageOutputTag.RDP_STREAM);
                DataStream<Row> vncStream = mainStream.getSideOutput(MessageOutputTag.VNC_STREAM);
                DataStream<Row> xdmcpStream = mainStream.getSideOutput(MessageOutputTag.XDMCP_STREAM);
                DataStream<Row> ntpStream = mainStream.getSideOutput(MessageOutputTag.NTP_STREAM);
                DataStream<Row> icmpStream = mainStream.getSideOutput(MessageOutputTag.ICMP_STREAM);

                DataStream<Row> s7Stream = mainStream.getSideOutput(MessageOutputTag.S7_STREAM);
                DataStream<Row> modbusStream = mainStream.getSideOutput(MessageOutputTag.MODBUS_STREAM);
                DataStream<Row> iec104Stream = mainStream.getSideOutput(MessageOutputTag.IEC104_STREAM);
                DataStream<Row> eipStream = mainStream.getSideOutput(MessageOutputTag.EIP_STREAM);
                DataStream<Row> opcStream = mainStream.getSideOutput(MessageOutputTag.OPC_STREAM);
                DataStream<Row> espStream = mainStream.getSideOutput(MessageOutputTag.ESP_STREAM);
                DataStream<Row> l2tpStream = mainStream.getSideOutput(MessageOutputTag.L2TP_STREAM);

                // 1. 首先将原始数据写入ODS层
                log.info("将原始数据写入ODS层");

                // 配置并创建原始数据Doris Sink - 为每个流单独配置
                configureDorisSink(dnsStream, CONFIG, MessageType.DNS);
                configureDorisSink(httpStream, CONFIG, MessageType.HTTP);
                configureDorisSink(sslStream, CONFIG, MessageType.SSL);
                configureDorisSink(sessionStream, CONFIG, MessageType.SESSION);
                configureDorisSink(sshStream, CONFIG, MessageType.SSH);
                configureDorisSink(rloginStream, CONFIG, MessageType.RLOGIN);
                configureDorisSink(telnetStream, CONFIG, MessageType.TELNET);
                configureDorisSink(rdpStream, CONFIG, MessageType.RDP);
                configureDorisSink(vncStream, CONFIG, MessageType.VNC);
                configureDorisSink(xdmcpStream, CONFIG, MessageType.XDMCP);
                configureDorisSink(ntpStream, CONFIG, MessageType.NTP);
                configureDorisSink(icmpStream, CONFIG, MessageType.ICMP);

                configureDorisSink(s7Stream, CONFIG, MessageType.S7);
                configureDorisSink(modbusStream, CONFIG, MessageType.MODBUS);
                configureDorisSink(iec104Stream, CONFIG, MessageType.IEC104);
                configureDorisSink(eipStream, CONFIG, MessageType.EIP);
                configureDorisSink(opcStream, CONFIG, MessageType.OPC);
                configureDorisSink(espStream, CONFIG, MessageType.ESP);
                configureDorisSink(l2tpStream, CONFIG, MessageType.L2TP);

                // 配置并创建原始数据Kafka Sink - 为每个流单独配置
                KafkaSinkManager.configureKafkaSink(dnsStream, CONFIG, "dns");
                KafkaSinkManager.configureKafkaSink(httpStream, CONFIG, "http");
                KafkaSinkManager.configureKafkaSink(sslStream, CONFIG, "ssl");
                KafkaSinkManager.configureKafkaSink(sessionStream, CONFIG, "session");
                KafkaSinkManager.configureKafkaSink(sshStream, CONFIG, "ssh");
                KafkaSinkManager.configureKafkaSink(rloginStream, CONFIG, "rlogin");
                KafkaSinkManager.configureKafkaSink(telnetStream, CONFIG, "telnet");
                KafkaSinkManager.configureKafkaSink(rdpStream, CONFIG, "rdp");
                KafkaSinkManager.configureKafkaSink(vncStream, CONFIG, "vnc");
                KafkaSinkManager.configureKafkaSink(xdmcpStream, CONFIG, "xdmcp");
                KafkaSinkManager.configureKafkaSink(ntpStream, CONFIG, "ntp");
                KafkaSinkManager.configureKafkaSink(icmpStream, CONFIG, "icmp");

                KafkaSinkManager.configureKafkaSink(s7Stream, CONFIG, "s7");
                KafkaSinkManager.configureKafkaSink(modbusStream, CONFIG, "modbus");
                KafkaSinkManager.configureKafkaSink(iec104Stream, CONFIG, "iec104");
                KafkaSinkManager.configureKafkaSink(eipStream, CONFIG, "eip");
                KafkaSinkManager.configureKafkaSink(opcStream, CONFIG, "opc");
                KafkaSinkManager.configureKafkaSink(espStream, CONFIG, "esp");
                KafkaSinkManager.configureKafkaSink(l2tpStream, CONFIG, "l2tp");

                // 2. 处理维度数据 - 从各个协议流中提取维度信息并写入DIM层
                log.info("从原始数据中提取维度信息并写入DIM层");

                // 为维度数据创建Doris Sink
                int dimSinkParallelism = CONFIG.getInt("doris.sink.dim.parallelism", 4);

                // 创建各种维度表的Sink
                var ipv4DimensionSink = FlinkDorisSinkManager.buildDorisSink("dim_ipv4", CONFIG);
                var ipv6DimensionSink = FlinkDorisSinkManager.buildDorisSink("dim_ipv6", CONFIG);
                var macDimensionSink = FlinkDorisSinkManager.buildDorisSink("dim_mac", CONFIG);
                var domainDimensionSink = FlinkDorisSinkManager.buildDorisSink("dim_domain", CONFIG);
                var registrableDomainDimensionSink = FlinkDorisSinkManager.buildDorisSink("dim_registrable_domain",
                                CONFIG);
                var urlDimensionSink = FlinkDorisSinkManager.buildDorisSink("dim_url", CONFIG);
                var appServiceDimensionSink = FlinkDorisSinkManager.buildDorisSink("dim_appservice", CONFIG);
                var appDimensionSink = FlinkDorisSinkManager.buildDorisSink("dim_app", CONFIG);
                var uaDimensionSink = FlinkDorisSinkManager.buildDorisSink("dim_ua", CONFIG);
                var osDimensionSink = FlinkDorisSinkManager.buildDorisSink("dim_os", CONFIG);
                var deviceDimensionSink = FlinkDorisSinkManager.buildDorisSink("dim_device", CONFIG);
                var fingerDimensionSink = FlinkDorisSinkManager.buildDorisSink("dim_sslfingerprint", CONFIG);

                // 处理DNS维度数据
                DimensionProcessor.processDnsDimensions(
                                dnsStream,
                                domainDimensionSink,
                                registrableDomainDimensionSink,
                                ipv4DimensionSink,
                                ipv6DimensionSink,
                                dimSinkParallelism);

                // 处理HTTP维度数据
                DimensionProcessor.processHttpDimensions(
                                httpStream,
                                urlDimensionSink,
                                uaDimensionSink,
                                osDimensionSink,
                                deviceDimensionSink,
                                dimSinkParallelism);

                // 处理SSL维度数据
                DimensionProcessor.processSSLDimensions(
                                sslStream,
                                domainDimensionSink,
                                registrableDomainDimensionSink,
                                fingerDimensionSink,
                                dimSinkParallelism);

                // 处理会话维度数据
                DimensionProcessor.processSessionDimensions(
                                sessionStream,
                                ipv4DimensionSink,
                                ipv6DimensionSink,
                                macDimensionSink,
                                appServiceDimensionSink,
                                appDimensionSink,
                                dimSinkParallelism);

                // 3. 将多个协议流合并为DWD层的会话日志表
                log.info("将多个协议流合并为DWD层的会话日志表");

                // 将每个协议流包装为StreamData，然后合并
                DataStream<StreamData> dnsStreamData = dnsStream
                                .map(row -> StreamData.of(MessageType.DNS, row))
                                .name("Wrap DNS Stream Data");

                DataStream<StreamData> httpStreamData = httpStream
                                .map(row -> StreamData.of(MessageType.HTTP, row))
                                .name("Wrap HTTP Stream Data");

                DataStream<StreamData> sslStreamData = sslStream
                                .map(row -> StreamData.of(MessageType.SSL, row))
                                .name("Wrap SSL Stream Data");

                DataStream<StreamData> sessionStreamData = sessionStream
                                .map(row -> StreamData.of(MessageType.SESSION, row))
                                .name("Wrap Session Stream Data");

                DataStream<StreamData> sshStreamData = sshStream
                                .map(row -> StreamData.of(MessageType.SSH, row))
                                .name("Wrap SSH Stream Data");

                DataStream<StreamData> rloginStreamData = rloginStream
                                .map(row -> StreamData.of(MessageType.RLOGIN, row))
                                .name("Wrap RLOGIN Stream Data");

                DataStream<StreamData> telnetStreamData = telnetStream
                                .map(row -> StreamData.of(MessageType.TELNET, row))
                                .name("Wrap TELNET Stream Data");

                DataStream<StreamData> rdpStreamData = rdpStream
                                .map(row -> StreamData.of(MessageType.RDP, row))
                                .name("Wrap RDP Stream Data");

                DataStream<StreamData> vncStreamData = vncStream
                                .map(row -> StreamData.of(MessageType.VNC, row))
                                .name("Wrap VNC Stream Data");

                DataStream<StreamData> xdmcpStreamData = xdmcpStream
                                .map(row -> StreamData.of(MessageType.XDMCP, row))
                                .name("Wrap XDMCP Stream Data");

                DataStream<StreamData> ntpStreamData = ntpStream
                                .map(row -> StreamData.of(MessageType.NTP, row))
                                .name("Wrap NTP Stream Data");

                DataStream<StreamData> icmpStreamData = icmpStream
                                .map(row -> StreamData.of(MessageType.ICMP, row))
                                .name("Wrap ICMP Stream Data");

                DataStream<StreamData> s7StreamData = s7Stream
                                .map(row -> StreamData.of(MessageType.S7, row))
                                .name("Wrap s7 Stream Data");
                DataStream<StreamData> modbusStreamData = modbusStream
                                .map(row -> StreamData.of(MessageType.MODBUS, row))
                                .name("Wrap modbus Stream Data");
                DataStream<StreamData> iec104StreamData = iec104Stream
                                .map(row -> StreamData.of(MessageType.IEC104, row))
                                .name("Wrap iec104 Stream Data");
                DataStream<StreamData> eipStreamData = eipStream
                                .map(row -> StreamData.of(MessageType.EIP, row))
                                .name("Wrap eip Stream Data");
                DataStream<StreamData> opcStreamData = opcStream
                                .map(row -> StreamData.of(MessageType.OPC, row))
                                .name("Wrap opc Stream Data");
                DataStream<StreamData> espStreamData = espStream
                                .map(row -> StreamData.of(MessageType.ESP, row))
                                .name("Wrap esp Stream Data");
                DataStream<StreamData> l2tpStreamData = l2tpStream
                                .map(row -> StreamData.of(MessageType.L2TP, row))
                                .name("Wrap l2tp Stream Data");

                // 合并所有流数据
                DataStream<StreamData> allStreamData = dnsStreamData
                                .union(httpStreamData, sslStreamData, sessionStreamData,
                                                sshStreamData, rloginStreamData, telnetStreamData,
                                                rdpStreamData, vncStreamData, xdmcpStreamData,
                                                ntpStreamData, icmpStreamData, s7StreamData, modbusStreamData,
                                                iec104StreamData, eipStreamData, opcStreamData, espStreamData,
                                                l2tpStreamData);

                // 按session_id分组并使用DWD会话处理器进行聚合
                DataStream<Row> dwdSessionStream = allStreamData
                                .keyBy(streamData -> {
                                        String sessionId = streamData.getSessionId();
                                        if (sessionId == null) {
                                                log.warn("无法获取session_id，使用默认值");
                                                return "unknown";
                                        }
                                        return sessionId;
                                })
                                .process(new DwdSessionProcessor())
                                .name("DWD Session Aggregation")
                                .setParallelism(dimSinkParallelism);

                // 创建DWD层会话日志表的Sink
                var dwdSessionLogsSink = FlinkDorisSinkManager.buildDorisSink("dwd_session_logs", CONFIG);

                // 将聚合后的会话数据写入DWD层
                dwdSessionStream
                                .sinkTo(dwdSessionLogsSink)
                                .name("DWD Session Logs to Doris")
                                .setParallelism(dimSinkParallelism);

                // 启动执行
                env.execute("Traffic ETL Processing Pipeline");
        }

        /**
         * 创建Kafka数据源
         *
         * @param env 流执行环境
         * @return Kafka数据流
         */
        private static DataStream<ZMPNMsg.JKNmsg> createKafkaSource(StreamExecutionEnvironment env) {
                KafkaSource<ZMPNMsg.JKNmsg> source = KafkaSource.<ZMPNMsg.JKNmsg>builder()
                                .setBootstrapServers(CONFIG.get(ConfigConstants.KAFKA_BOOTSTRAP_SERVERS))
                                .setTopics(Collections.singletonList(CONFIG.get(ConfigConstants.KAFKA_TOPIC_META)))
                                .setStartingOffsets(OffsetsInitializer.committedOffsets(OffsetResetStrategy.EARLIEST))
                                .setDeserializer(new ProtobufTrafficMetadataDeserializer())
                                .setGroupId(CONFIG.get(ConfigConstants.KAFKA_GROUP_ID))
                                .setProperties(createConsumerProperties())
                                .build();

                return env.fromSource(
                                source,
                                WatermarkStrategy.noWatermarks(),
                                "Kafka-Source",
                                TypeInformation.of(ZMPNMsg.JKNmsg.class))
                                .setParallelism(PARALLELISM_KAFKA_SOURCE_VALUE).name("Probe Kafka Topic meta");
        }

        /**
         * 创建Kafka消费者配置
         *
         * @return Kafka消费者配置属性
         */
        private static Properties createConsumerProperties() {
                Properties properties = new Properties();
                // 增加请求超时时间，提高稳定性
                properties.put(ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG, "60000");
                // 设置批量获取大小，提高吞吐量
                properties.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, "500");
                // 设置心跳间隔，提高稳定性
                properties.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, "3000");
                // 设置会话超时时间
                properties.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, "30000");
                // 设置最大拉取等待时间
                properties.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, "500");

                // 添加Kafka安全配置
                String securityProtocol = CONFIG.get(ConfigConstants.KAFKA_SECURITY_PROTOCOL, "");
                if (securityProtocol != null && !securityProtocol.isEmpty()) {
                        log.info("Configuring Kafka security with protocol: {}", securityProtocol);

                        // 设置安全协议
                        properties.put("security.protocol", securityProtocol);

                        // 设置SASL机制
                        String saslMechanism = CONFIG.get(ConfigConstants.KAFKA_SASL_MECHANISM, "");
                        if (saslMechanism != null && !saslMechanism.isEmpty()) {
                                properties.put("sasl.mechanism", saslMechanism);
                        }

                        // 设置JAAS配置
                        String username = CONFIG.get(ConfigConstants.KAFKA_CLIENT_USER, "");
                        String password = CONFIG.get(ConfigConstants.KAFKA_CLIENT_PASSWORD, "");

                        if (username != null && !username.isEmpty() && password != null) {
                                String jaasConfig = String.format(
                                                "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"%s\" password=\"%s\";",
                                                username, password);
                                properties.put("sasl.jaas.config", jaasConfig);
                                log.info("Configured SASL authentication for user: {}", username);
                        } else {
                                log.warn("Kafka security credentials not fully configured. Username or password missing.");
                        }
                }

                return properties;
        }

}
