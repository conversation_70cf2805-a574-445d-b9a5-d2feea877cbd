package com.geeksec.nta.trafficetl.etl.dwd.processor;

import com.geeksec.nta.trafficetl.etl.dwd.aggregator.SessionAggregator;
import com.geeksec.nta.trafficetl.etl.dwd.converter.DwdRowConverter;
import com.geeksec.nta.trafficetl.etl.dwd.model.StreamData;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.time.Duration;

/**
 * DWD层会话处理器
 * 使用StreamData包装类，基于OutputTag的消息类型识别
 * 负责将ODS层的多个协议流合并为DWD层的会话日志
 * 使用Flink State管理会话状态，支持会话超时处理
 *
 * <AUTHOR>
 */
@Slf4j
public class DwdSessionProcessor extends KeyedProcessFunction<String, StreamData, Row> {

    private static final long serialVersionUID = 1L;

    /** 会话超时时间（毫秒） */
    private static final long SESSION_TIMEOUT_MS = Duration.ofMinutes(5).toMillis();

    /** 会话聚合器状态 */
    private transient ValueState<SessionAggregator> sessionState;

    /** DWD行转换器 */
    private final DwdRowConverter dwdRowConverter = new DwdRowConverter();

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 初始化会话状态
        ValueStateDescriptor<SessionAggregator> sessionStateDescriptor =
            new ValueStateDescriptor<>(
                "session-aggregator-state",
                TypeInformation.of(SessionAggregator.class)
            );
        sessionState = getRuntimeContext().getState(sessionStateDescriptor);
    }

    @Override
    public void processElement(StreamData streamData, Context ctx, Collector<Row> out) throws Exception {
        String sessionId = streamData.getSessionId();
        if (sessionId == null) {
            log.warn("收到没有session_id的数据，跳过处理");
            return;
        }

        // 获取或创建会话聚合器
        SessionAggregator aggregator = sessionState.value();
        if (aggregator == null) {
            aggregator = new SessionAggregator(sessionId);
            log.debug("创建新的会话聚合器: {}", sessionId);
        }

        // 添加流数据到聚合器
        aggregator.addStreamData(streamData);

        // 更新状态
        sessionState.update(aggregator);

        // 设置定时器，用于会话超时处理
        long currentTime = ctx.timestamp() != null ? ctx.timestamp() : System.currentTimeMillis();
        long timerTime = currentTime + SESSION_TIMEOUT_MS;
        ctx.timerService().registerProcessingTimeTimer(timerTime);

        log.debug("处理会话数据: sessionId={}, messageType={}, 当前协议数量={}",
                sessionId, streamData.getMessageType(), aggregator.getProtocolCount());
    }

    @Override
    public void onTimer(long timestamp, OnTimerContext ctx, Collector<Row> out) throws Exception {
        SessionAggregator aggregator = sessionState.value();
        if (aggregator != null) {
            // 会话超时，输出聚合结果
            Row dwdRow = dwdRowConverter.convert(aggregator);
            if (dwdRow != null) {
                out.collect(dwdRow);
                log.debug("会话超时输出: sessionId={}, 协议数量={}",
                        aggregator.getSessionId(), aggregator.getProtocolCount());
            }

            // 清理状态
            sessionState.clear();
        }
    }
}
