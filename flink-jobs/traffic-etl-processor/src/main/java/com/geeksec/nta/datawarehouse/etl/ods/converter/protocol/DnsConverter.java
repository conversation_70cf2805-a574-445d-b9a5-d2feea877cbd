package com.geeksec.nta.datawarehouse.etl.ods.converter.protocol;

import com.geeksec.common.toolkit.time.TimeUtils;
import com.geeksec.nta.datawarehouse.etl.constant.FieldConstants;
import com.geeksec.nta.datawarehouse.etl.ods.converter.common.AbstractProtobufMessageConverter;
import com.geeksec.nta.datawarehouse.etl.ods.tag.MessageOutputTag;
import com.geeksec.proto.ZMPNMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * Converter for DNS protocol messages.
 * Maps DNS protocol data from protobuf messages to Doris
 * ods_dns_protocol_metadata table format.
 * This converter is aligned with the latest ods_dns_protocol_metadata table
 * schema.
 *
 * <AUTHOR> Team
 */
@Slf4j
public class DnsConverter extends AbstractProtobufMessageConverter {

    @Override
    protected Row convertMessage(ZMPNMsg.JKNmsg msg) {
        if (!msg.hasDns()) {
            log.warn("JKNmsg does not contain Dns message");
            return null;
        }
        Row row = Row.withNames();
        // 创建带有命名字段的Row
        ZMPNMsg.dns_msg dns = msg.getDns();
        if (dns.hasCommMsg()){
            enrichComMsg(row, dns.getCommMsg());
        }

        // 设置DNS特定字段
        row.setField(FieldConstants.FIELD_DNS_ID, dns.getDnsId());
        row.setField(FieldConstants.FIELD_DNS_FLAGS, dns.getDnsFlags());
        row.setField(FieldConstants.FIELD_DNS_QUE, dns.getDnsQue());
        row.setField(FieldConstants.FIELD_DNS_ANS, dns.getDnsAns());
        row.setField(FieldConstants.FIELD_DNS_AUTH, dns.getDnsAuth());
        row.setField(FieldConstants.FIELD_DNS_ADD, dns.getDnsAdd());
        row.setField(FieldConstants.FIELD_DNS_DOMAIN, dns.getDnsDomain());
        row.setField(FieldConstants.FIELD_DNS_DOMAIN_IP, dns.getDnsDomainIp());
        row.setField(FieldConstants.FIELD_DNS_QUERY, dns.getDnsQuery());
        row.setField(FieldConstants.FIELD_DNS_ANSWER, dns.getDnsAnswer());

        return row;
    }

    @Override
    public OutputTag<Row> getOutputTag() {
        return MessageOutputTag.DNS_STREAM;
    }
}
