package com.geeksec.nta.datawarehouse.etl.dim.function;

import com.geeksec.nta.datawarehouse.etl.constant.FieldConstants;
import com.geeksec.nta.datawarehouse.etl.dim.DimensionProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Set;

import static com.geeksec.common.toolkit.time.TimeUtils.DORIS_DATETIME_FORMATTER;

/**
 * 应用维度表处理函数，用于生成符合dim_app表结构的维度数据
 * <p>
 * 使用Flink Backend State进行缓存管理，支持TTL自动清理
 *
 * <AUTHOR>
 */
@Slf4j
public class AppDimensionTableFunction extends ProcessFunction<Row, Row> {
    private static final long serialVersionUID = 1L;

    /**
     * 应用维度数据输出标签
     */
    public static final OutputTag<Row> APP_DIM_TAG = new OutputTag<Row>("app-dimension") {};
    /**
     * 应用顶点数据输出标签
     */
    public static final OutputTag<Row> APP_VERTEX_TAG = new OutputTag<Row>("app-tag") {};
    public static final String DEFAULT_APP_VERSION = "1.0";

    private final String appNameFieldName;
    private final Duration ttl;

    /**
     * 缓存应用维度数据的状态
     */
    private transient MapState<String, Map<String, Object>> appDimensionState;

    /**
     * 构造函数
     *
     * @param appNameFieldName 应用名称字段名
     */
    public AppDimensionTableFunction(String appNameFieldName) {
        // 默认24小时TTL
        this(appNameFieldName, Duration.ofHours(24));
    }

    /**
     * 构造函数
     *
     * @param appNameFieldName 应用名称字段名
     * @param ttl              状态TTL时间
     */
    public AppDimensionTableFunction(String appNameFieldName, Duration ttl) {
        this.appNameFieldName = appNameFieldName;
        this.ttl = ttl;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 配置状态TTL
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(ttl)
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();

        // 初始化应用维度状态
        MapStateDescriptor<String, Map<String, Object>> appStateDescriptor =
                new MapStateDescriptor<>("app-dimension-state",
                        TypeInformation.of(new TypeHint<String>() {}),
                        TypeInformation.of(new TypeHint<Map<String, Object>>() {}));
        appStateDescriptor.enableTimeToLive(ttlConfig);
        appDimensionState = getRuntimeContext().getMapState(appStateDescriptor);
    }

    @Override
    public void processElement(Row value, Context ctx, Collector<Row> out) throws Exception {
        try {
            String appName = getStringFieldValue(value, appNameFieldName);
            
            if (appName == null || appName.trim().isEmpty()) {
                log.warn("应用名为空，跳过处理");
                out.collect(value);
                return;
            }

            // 更新state map状态
            try {
                Map<String, Object> existingAppInfo = appDimensionState.get(appName);
                if (existingAppInfo == null) {
                    appDimensionState.put(appName, Map.of(
                            FieldConstants.KEY_APP_NAME, appName,
                            FieldConstants.KEY_APP_VERSION, DEFAULT_APP_VERSION,
                            FieldConstants.KEY_DW_CREATION_TIMESTAMP, LocalDateTime.now().format(DORIS_DATETIME_FORMATTER),
                            FieldConstants.KEY_DW_LAST_UPDATED_TIMESTAMP, LocalDateTime.now().format(DORIS_DATETIME_FORMATTER)));
                } else {
                    appDimensionState.get(appName).put(FieldConstants.KEY_DW_LAST_UPDATED_TIMESTAMP, LocalDateTime.now().format(DORIS_DATETIME_FORMATTER));
                }
            } catch (Exception e) {
                log.error("更新状态数据时发生错误: {}", e.getMessage());
            }

            // 创建维度表记录侧边流
            ctx.output(APP_DIM_TAG, createDimensionRow(appName));
            ctx.output(APP_VERTEX_TAG, createVertexRow(appName));
            // 继续传递原始数据
            out.collect(value);
        } catch (Exception e) {
            log.error("处理应用维度数据时发生错误: {}", e.getMessage());
            out.collect(value);
        }
    }

    /**
     * 创建符合维度表结构的应用维度记录
     *
     * @param appName 应用名称
     * @return 符合dim_app表结构的Row
     */
    private Row createDimensionRow(String appName) {
        try {
            Map<String, Object> appStateMap = appDimensionState.get(appName);

            Row dimensionRow = Row.withNames();
            dimensionRow.setField("app_name", appStateMap.get(FieldConstants.KEY_APP_NAME));
            dimensionRow.setField("app_version", appStateMap.get(FieldConstants.KEY_APP_VERSION));
            DimensionProcessor.insertTimeStateMap(appDimensionState, appName, dimensionRow);
            return dimensionRow;
        } catch (Exception e) {
            log.error("创建应用维度数据时发生错误: {}", e.getMessage());
            Row dimensionRow = Row.withNames();
            dimensionRow.setField("app_name", appName);
            dimensionRow.setField("app_version", DEFAULT_APP_VERSION);
            DimensionProcessor.insertTimeStateMap(appDimensionState, appName, dimensionRow);

            return dimensionRow;
        }
    }

    /**
     * 创建符合图空间APP TAG结构的应用VERTEX记录
     *
     * @param appName 应用名称
     * @return 符合dim_app表结构的Row
     */
    private Row createVertexRow(String appName) {
        // vid app_name app_version threat_score trust_score
        try {
            Map<String, Object> appStateMap = appDimensionState.get(appName);

            return Row.of(appStateMap.get(FieldConstants.KEY_APP_NAME).toString() + appStateMap.get(FieldConstants.KEY_APP_VERSION).toString(),
                    appStateMap.get(FieldConstants.KEY_APP_NAME), appStateMap.get(FieldConstants.KEY_APP_VERSION), 0, 100);
        } catch (Exception e) {
            log.error("创建应用VERTEX数据时发生错误: {}", e.getMessage());

            return Row.of(appName + DEFAULT_APP_VERSION, appName, DEFAULT_APP_VERSION, 0, 100);
        }
    }

    /**
     * 从Row中获取字符串字段值
     *
     * @param row       Row对象
     * @param fieldName 字段名
     * @return 字段值，如果不存在或类型不匹配则返回null
     */
    private String getStringFieldValue(Row row, String fieldName) {
        try {
            Set<String> fieldNames = row.getFieldNames(true);
            if (fieldNames != null && fieldNames.contains(fieldName)) {
                Object fieldValue = row.getField(fieldName);
                if (fieldValue instanceof String) {
                    return (String) fieldValue;
                }
            }
        } catch (Exception e) {
            log.warn("获取字符串字段值时发生错误，字段名: {}, 错误: {}", fieldName, e.getMessage());
        }
        return null;
    }
}
