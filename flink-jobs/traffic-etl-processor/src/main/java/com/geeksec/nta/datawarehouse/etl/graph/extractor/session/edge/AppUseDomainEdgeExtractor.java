package com.geeksec.nta.datawarehouse.etl.graph.extractor.session.edge;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import com.geeksec.common.infrastructure.network.DomainUtils;
import com.geeksec.nta.datawarehouse.etl.constant.FieldConstants;
import com.geeksec.nta.datawarehouse.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.datawarehouse.sink.tag.NebulaGraphOutputTag;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description null.java
 * @Date 9:30$ 2025/6/18$
 **/
@Slf4j
public class AppUseDomainEdgeExtractor extends BaseEdgeExtractor {
    // 默认版本
    public static final String APP_DEFAULT_VERSION = "1.0";

    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.APP_USES_DOMAIN_TAG;
    }

    /**
     * 域名关联应用 (DOMAIN -> APP)
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        List<Row> edges = new ArrayList<>();
        // 获取应用信息
        String appVidKey = "";
        String appName = value.getField(FieldConstants.FIELD_APP_NAME).toString();
        // 过滤不是App应用的会话
        if (appName.contains("_")) {
            return Collections.emptyList();
        } else {
            // 处理App vid
            appVidKey = appName + "_" + APP_DEFAULT_VERSION;
        }
        // 获取域名列表
        Set<String> domainSet = getSessionDomain(value);
        for (String domain : domainSet) {
            domain = DomainUtils.formatDomain(domain);
            edges.add(Row.of(domain, appVidKey,
                    0 // rank暂定0
            ));
        }
        return edges;
    }
}
