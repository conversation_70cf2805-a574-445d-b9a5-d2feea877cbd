package com.geeksec.nta.trafficetl.etl.ods.converter.protocol;

import com.geeksec.common.toolkit.time.TimeUtils;
import com.geeksec.nta.trafficetl.etl.constant.FieldConstants;
import com.geeksec.nta.trafficetl.etl.ods.converter.common.AbstractProtobufMessageConverter;
import com.geeksec.nta.trafficetl.etl.ods.tag.MessageOutputTag;
import com.geeksec.proto.ZMPNMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * IEC104协议转换器
 * 将IEC104协议的protobuf消息转换为Doris ods_iec104_protocol_metadata表格式
 *
 * <AUTHOR>
 */
@Slf4j
public class Iec104Converter extends AbstractProtobufMessageConverter {

    @Override
    protected Row convertMessage(ZMPNMsg.JKNmsg msg) {
        if (!msg.hasIec104()) {
            log.warn("JKNmsg does not contain Iec104 message");
            return null;
        }
        Row row = Row.withNames();
        ZMPNMsg.iec104_msg iec104Msg = msg.getIec104();
        if (iec104Msg.hasCommMsg()){
            // 设置通用字段
            enrichComMsg(row, iec104Msg.getCommMsg());
        }

        // 设置IEC104特定字段（需要根据实际protobuf定义实现）
        // TODO: 根据实际的IEC104 protobuf消息结构实现字段映射
        row.setField(FieldConstants.TRANS_ID,iec104Msg.getTransId());
        row.setField(FieldConstants.PROTOCOL_ID,iec104Msg.getProtocolId());
        row.setField(FieldConstants.SLAVE_ID,iec104Msg.getSlaveId());
        row.setField(FieldConstants.FUNC_CODE,iec104Msg.getFuncCode());
        return row;
    }

    @Override
    public OutputTag<Row> getOutputTag() {
        return MessageOutputTag.IEC104_STREAM;
    }
}
