package com.geeksec.nta.trafficetl.etl.graph.extractor.dns.edge;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.common.toolkit.crypto.HashUtils;
import com.geeksec.common.utils.net.DomainUtils;
import com.geeksec.nta.trafficetl.etl.constant.FieldConstants;
import com.geeksec.nta.trafficetl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.trafficetl.etl.graph.extractor.dns.DNSEdgeExtractor;
import com.geeksec.nta.trafficetl.sink.tag.NebulaGraphOutputTag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @Description null.java
 * @Date 9:30$ 2025/6/18$
 **/
@Slf4j
public class ClientQueriesDnsServerEdgeExtractor extends BaseEdgeExtractor {
    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.CLIENT_QUERIES_DNS_SERVER_TAG;
    }

    /**
     * 向DNS服务器查询 (源IP -> 目的IP)
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        // 获取源IP和目的IP
        String sIP = value.getField(FieldConstants.FIELD_SRC_IP).toString();
        String dIP = value.getField(FieldConstants.FIELD_DST_IP).toString();

        // 获取Query和Answer中的type信息
        int queryType = DNSEdgeExtractor.extractQueryType(value);
        int answerType = DNSEdgeExtractor.extractAnswerType(value);
        if (queryType == -1){
            return Collections.emptyList();
        }
        return List.of(Row.of(sIP, dIP,
                0, // rank暂定0
                queryType, answerType
        ));

    }
}
