package com.geeksec.nta.trafficetl.etl.graph.extractor.session.edge;

import com.geeksec.nta.trafficetl.etl.constant.FieldConstants;
import com.geeksec.nta.trafficetl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.trafficetl.sink.tag.NebulaGraphOutputTag;
import com.geeksec.proto.ZMPNMsg;
import nl.basjes.parse.useragent.UserAgent;
import nl.basjes.parse.useragent.UserAgentAnalyzer;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description null.java
 * @Date 9:30$ 2025/6/18$
 **/
public class UAHasAppEdgeExtractor extends BaseEdgeExtractor {
    public static UserAgentAnalyzer analyzer = UserAgentAnalyzer
            .newBuilder()
            .withCache(10000) // 设置缓存大小
            .build();

    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.UA_HAS_APP_TAG;
    }

    /**
     * UA包含应用信息 (UA -> APP)
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        List<Row> edges = new ArrayList<>();
        // 获取应用信息
        String dIp = value.getField(FieldConstants.FIELD_DST_IP).toString();
        Integer dPort = Integer.valueOf(value.getField(FieldConstants.FIELD_DST_PORT).toString());
        String appName = value.getField(FieldConstants.FIELD_APP_NAME).toString();
        String appKey = dIp + "_" + dPort + "_" + appName;
        // 从HTTP信息中获取UA信息
        List<ZMPNMsg.single_http> httpInfoList = (List<ZMPNMsg.single_http>) value.getField(FieldConstants.SESSION_HTTP_LIST);
        if (CollectionUtils.isNotEmpty(httpInfoList)) {
            for (ZMPNMsg.single_http singleHttp : httpInfoList) {
                String userAgent = (String) singleHttp.getUserAgent();
                if (!StringUtil.isNullOrEmpty(userAgent)) {
                    // 解析UA信息
                    UserAgent agent = analyzer.parse(userAgent);
                    String osName = agent.getValue("OperatingSystemName"); // 操作系统
                    String deviceName = agent.getValue("DeviceName"); // 设备名称
                    String applicationName = agent.getValue("AgentName"); // 浏览器/应用名称
                    String userAgentKey = applicationName + "_" + osName + "_" + deviceName;
                    edges.add(Row.of(userAgentKey, appKey,
                            0 // rank 暂定0
                    ));
                }
            }
        }
        return edges;
    }
}
