# 流量ETL处理器 (Traffic ETL Processor)

## 项目概述

流量ETL处理器是基于Apache Flink构建的实时流处理作业，负责处理网络流量分析系统中的原始协议数据，并将其转换为分层数据仓库架构中的各层数据。该项目采用现代化的数据仓库分层架构，实现了从原始数据到业务价值的完整数据处理链路。

## 分层数据仓库架构

本项目采用经典的数据仓库分层架构，确保数据的质量、一致性和可扩展性：

### 1. ODS层 (Operational Data Store) - 原始数据层
- **目的**: 保持数据的原始格式，作为数据的"单一真实来源"
- **特点**:
  - 数据结构与源系统保持一致
  - 最小化的数据转换
  - 完整保留原始数据信息
- **数据来源**: Kafka中的Protobuf格式网络流量数据
- **表结构**:
  - `ods_single_session_logs` - 会话日志原始数据
  - `ods_*_protocol_metadata` - 各协议元数据原始数据

### 2. DIM层 (Dimension) - 维度数据层
- **目的**: 存储相对静态的资产信息和维度数据
- **特点**:
  - 支持缓慢变化维度(SCD)
  - 提供数据丰富化的基础
  - 使用聚合键优化查询性能
- **主要维度表**:
  - `dim_ipv4/dim_ipv6` - IP地址维度(地理位置、ASN信息等)
  - `dim_domain` - 域名维度(威胁评分、Alexa排名等)
  - `dim_mac` - MAC地址维度
  - `dim_cert` - SSL证书维度
  - `dim_app/dim_os/dim_device` - 应用、操作系统、设备维度

### 3. DWD层 (Data Warehouse Detail) - 明细数据层
- **目的**: 存储经过清洗、转换和丰富化的明细数据
- **特点**:
  - 数据质量得到保证
  - 字段命名规范化
  - 集成维度信息
  - 支持业务分析需求
- **数据内容**:
  - 清洗后的会话数据
  - 丰富化的协议元数据
  - 标准化的字段格式

### 4. DWS层 (Data Warehouse Summary) - 汇总数据层 [规划中]
- **目的**: 存储按不同维度聚合的统计数据
- **特点**:
  - 预计算的聚合指标
  - 支持OLAP分析
  - 提升查询性能
- **规划内容**:
  - 按时间维度的流量统计
  - 按IP/域名的行为分析
  - 威胁检测相关指标

## ETL处理流程

### 数据流架构图
```
Kafka(Protobuf) → Flink Stream Processing → Doris Data Warehouse
     ↓
[消息路由] → [数据转换] → [维度丰富] → [质量检查] → [分层存储]
```

### 详细ETL流程

#### 1. 数据接入阶段
- **数据源**: Kafka Topic中的Protobuf格式消息
- **反序列化**: 使用`ProtobufTrafficMetadataDeserializer`将二进制数据转换为`ZMPNMsg.JKNmsg`对象
- **消息类型**: 支持20+种网络协议类型(DNS、HTTP、SSL、SSH、RDP等)

#### 2. 消息路由与转换阶段
- **路由器**: `MessageProcessor`根据消息类型将数据分发到不同的侧输出流
- **转换器工厂**: `ProtobufConverterFactory`为每种协议类型提供专用转换器
- **数据转换**: 各协议转换器将Protobuf消息转换为Flink Row格式
- **输出标签**: 使用`MessageOutputTag`管理不同协议的侧输出流

#### 3. 维度处理阶段
- **维度提取**: `DimensionProcessor`从原始数据中提取维度信息
- **维度更新**: 实时更新各维度表的统计信息
- **支持的维度**:
  - IP维度: 地理位置、ASN、威胁评分
  - 域名维度: DNS查询统计、威胁情报
  - 设备维度: 操作系统、应用识别

#### 4. 数据丰富化阶段
- **维度数据丰富**: 在维度数据提取过程中直接进行数据丰富化
- **地理位置丰富**: 基于GeoLite2数据库进行IP地理位置解析，集成到IP维度表中
- **域名信息丰富**: 提取基础域名等信息，集成到域名维度表中
- **统一丰富化**: 数据丰富化逻辑已整合到维度处理工具类中，避免重复处理

#### 5. 数据质量保证
- **数据验证**: 字段完整性和格式验证
- **异常处理**: 错误数据的识别和处理
- **监控指标**: 处理速度、错误率等关键指标

#### 6. 分层存储阶段
- **ODS存储**: 原始数据直接写入ODS层表
- **DWD存储**: 丰富化后的数据写入DWD层表
- **并行写入**: 同时支持Kafka和Doris的双写模式
- **分区策略**: 基于时间的动态分区管理

## 技术架构

### 核心技术栈
- **流处理引擎**: Apache Flink 1.17+
- **数据仓库**: Apache Doris
- **消息队列**: Apache Kafka
- **序列化**: Protocol Buffers
- **开发语言**: Java 17
- **构建工具**: Maven

### 关键组件

#### 数据源组件
- `ProtobufTrafficMetadataDeserializer`: Protobuf消息反序列化器
- `KafkaSource`: Kafka数据源配置

#### 处理组件
- `MessageProcessor`: 消息路由处理器
- `DimensionProcessor`: 维度数据处理器（已整合数据丰富化功能）

#### 转换组件
- `AbstractProtobufMessageConverter`: 抽象转换器基类
- 协议特定转换器: `DnsConverter`, `HttpConverter`, `SslConverter`等
- `ProtobufConverterFactory`: 转换器工厂

#### 输出组件
- `DorisSinkManager`: Doris数据库连接器管理
- `KafkaSinkManager`: Kafka输出管理器

### 设计模式
- **工厂模式**: 转换器创建和管理
- **策略模式**: 不同协议的处理策略
- **观察者模式**: 侧输出流的事件处理
- **建造者模式**: 复杂配置对象的构建

## 项目结构

```
src/main/java/com/geeksec/nta/datawarehouse/
├── app/                          # 应用入口
│   └── DataWarehousePipeline.java    # 主流水线类
├── common/                       # 公共组件
│   ├── MessageType.java              # 消息类型枚举
│   └── FieldConstants.java           # 字段常量定义
├── etl/                         # ETL处理组件
│   ├── ods/                         # ODS层处理
│   │   ├── processor/               # 消息处理器
│   │   ├── converter/               # 数据转换器
│   │   └── tag/                     # 输出标签
│   └── dim/                         # 维度处理
│       ├── DimensionProcessor.java   # 维度处理器
│       └── utils/                   # 维度工具类
│           ├── IpDimensionUtils.java    # IP维度工具
│           └── DomainDimensionUtils.java # 域名维度工具
├── sink/                        # 数据输出
│   ├── DorisSinkManager.java        # Doris连接器
│   └── KafkaSinkManager.java        # Kafka连接器
└── source/                      # 数据源
    └── ProtobufTrafficMetadataDeserializer.java
```

## 配置说明

### 核心配置项
```properties
# Kafka配置
kafka.bootstrap.servers=localhost:9092
kafka.group.id=data-warehouse-processor
kafka.topic.traffic-metadata=traffic-metadata

# Doris配置
doris.fenodes=localhost:8030
doris.database=nta
doris.username=root
doris.password=

# 并行度配置
parallelism.parsing=4
parallelism.enrichment=2
parallelism.sink=2

# 检查点配置
checkpoint.interval=60000
checkpoint.timeout=300000
```

## 部署与运行

### 环境要求
- JDK 17+
- Apache Flink 1.17+
- Apache Kafka 2.8+
- Apache Doris 1.2+

### 构建项目
```bash
mvn clean package -DskipTests
```

### 提交作业
```bash
flink run -c com.geeksec.nta.datawarehouse.app.DataWarehousePipeline \
  target/traffic-etl-processor-1.0-SNAPSHOT.jar
```

## 监控与运维

### 关键指标
- **吞吐量**: 每秒处理的消息数
- **延迟**: 端到端处理延迟
- **错误率**: 处理失败的消息比例
- **背压**: 流处理的背压情况

### 日志监控
- 使用结构化日志记录关键处理节点
- 支持分布式链路追踪
- 集成告警机制

## 支持的协议类型

本系统支持以下网络协议的解析和处理：

### 应用层协议
- **HTTP** (类型码: 80) - Web流量分析
- **DNS** (类型码: 4) - 域名解析分析
- **SSL/TLS** (类型码: 29) - 加密流量分析

### 远程访问协议
- **SSH** (类型码: 28) - 安全Shell连接
- **RDP** (类型码: 639) - 远程桌面协议
- **VNC** (类型码: 104) - 虚拟网络计算
- **TELNET** (类型码: 101) - 远程终端协议
- **RLOGIN** (类型码: 100) - 远程登录协议
- **XDMCP** (类型码: 105) - X显示管理器控制协议

### 工业控制协议
- **S7** - 西门子S7通信协议
- **Modbus** - 工业自动化协议
- **IEC104** - 电力系统通信协议
- **EIP** - 以太网/IP协议
- **OPC** - OLE过程控制协议

### 网络基础协议
- **ICMP** (类型码: 108) - 网络控制消息协议
- **NTP** (类型码: 106) - 网络时间协议
- **ESP** - 封装安全载荷协议
- **L2TP** - 第二层隧道协议

### 会话信息
- **CONNECT** (类型码: 30) - 连接会话信息

## 数据处理流程详解

### 消息类型识别
系统使用`MessageType`枚举来统一管理所有支持的协议类型，每个协议都有对应的类型码和专用的转换器。

### 协议转换器架构
每个协议都有专门的转换器类，继承自`AbstractProtobufMessageConverter`：
- `DnsConverter` - DNS协议转换器
- `HttpConverter` - HTTP协议转换器
- `SslConverter` - SSL/TLS协议转换器
- `SessionConverter` - 会话信息转换器
- 等等...

### 侧输出流管理
使用`MessageOutputTag`为每种协议类型创建专用的侧输出流，实现数据的并行处理和路由。

## 扩展计划

### DWS层开发计划
1. **实时指标计算**
   - 流量趋势分析
   - 异常行为检测
   - 威胁情报聚合

2. **OLAP支持**
   - 多维度分析
   - 钻取和上卷操作
   - 实时仪表板支持

3. **机器学习集成**
   - 异常检测模型
   - 行为基线建模
   - 威胁预测算法

### 性能优化计划
- 算子链优化
- 状态后端调优
- 资源动态调整
- 数据倾斜处理

### 新协议支持
- 添加更多工业协议支持
- 支持自定义协议解析
- 协议插件化架构

## 开发指南

### 添加新协议支持

1. **定义消息类型**
   ```java
   // 在MessageType枚举中添加新协议
   NEW_PROTOCOL(999)
   ```

2. **创建转换器**
   ```java
   public class NewProtocolConverter extends AbstractProtobufMessageConverter {
       @Override
       protected Row convertMessage(ZMPNMsg.JKNmsg msg) {
           // 实现协议特定的转换逻辑
       }

       @Override
       public OutputTag<Row> getOutputTag() {
           return MessageOutputTag.NEW_PROTOCOL_STREAM;
       }
   }
   ```

3. **注册转换器**
   ```java
   // 在ProtobufConverterFactory中添加case分支
   case NEW_PROTOCOL -> new NewProtocolConverter();
   ```

4. **创建输出标签**
   ```java
   // 在MessageOutputTag中定义
   public static final OutputTag<Row> NEW_PROTOCOL_STREAM =
       new OutputTag<Row>("new-protocol-stream") {};
   ```

### 代码规范
- 遵循Java 17编码规范
- 使用统一的代码格式化配置
- 编写完整的单元测试
- 添加详细的Javadoc注释
- 使用枚举而非字符串常量
- 优先使用不可变对象

### 测试指南
- 为每个转换器编写单元测试
- 使用测试数据验证ETL流程
- 进行端到端集成测试
- 性能测试和压力测试

## 故障排查

### 常见问题

1. **消息反序列化失败**
   - 检查Protobuf版本兼容性
   - 验证消息格式是否正确

2. **数据写入失败**
   - 检查Doris连接配置
   - 验证表结构是否匹配

3. **处理延迟过高**
   - 调整并行度配置
   - 检查是否存在数据倾斜

4. **内存溢出**
   - 调整JVM堆内存设置
   - 优化状态后端配置

### 日志分析
- 关注ERROR和WARN级别日志
- 监控处理速率和背压指标
- 使用Flink Web UI进行可视化分析

## 贡献指南

### 提交流程
1. Fork项目并创建特性分支
2. 完成开发并通过所有测试
3. 提交Pull Request
4. 代码审查通过后合并

### 代码审查要点
- 代码质量和规范性
- 测试覆盖率
- 性能影响评估
- 文档完整性