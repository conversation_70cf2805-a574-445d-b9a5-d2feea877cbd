package com.geeksec.nta.datawarehouse.etl.dim.function;

import com.geeksec.nta.datawarehouse.etl.dim.DimensionProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import java.time.Duration;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * URL维度表处理函数，用于生成符合dim_url表结构的维度数据
 * <p>
 * 使用Flink Backend State进行缓存管理，支持TTL自动清理
 *
 * <AUTHOR>
 */
@Slf4j
public class UrlDimensionTableFunction extends ProcessFunction<Row, Row> {
    private static final long serialVersionUID = 1L;

    /**
     * URL维度数据输出标签
     */
    public static final OutputTag<Row> URL_DIM_TAG = new OutputTag<Row>("url-dimension") {
    };
    public static final OutputTag<Row> URL_VERTEX_TAG = new OutputTag<Row>("url-tag") {
    };

    private final String urlFieldName;
    private final Duration ttl;

    /**
     * 缓存URL维度数据的状态
     */
    private transient MapState<String, Map<String, Object>> urlDimensionState;

    /**
     * 构造函数
     *
     * @param urlFieldName URL字段名
     */
    public UrlDimensionTableFunction(String urlFieldName) {
        // 默认12小时TTL
        this(urlFieldName, Duration.ofHours(12));
    }

    /**
     * 构造函数
     *
     * @param urlFieldName URL字段名
     * @param ttl          状态TTL时间
     */
    public UrlDimensionTableFunction(String urlFieldName, Duration ttl) {
        this.urlFieldName = urlFieldName;
        this.ttl = ttl;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 配置状态TTL
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(ttl)
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();

        // 初始化URL维度状态
        MapStateDescriptor<String, Map<String, Object>> urlStateDescriptor =
                new MapStateDescriptor<>("url-dimension-state",
                        TypeInformation.of(new TypeHint<String>() {
                        }),
                        TypeInformation.of(new TypeHint<Map<String, Object>>() {
                        }));
        urlStateDescriptor.enableTimeToLive(ttlConfig);
        urlDimensionState = getRuntimeContext().getMapState(urlStateDescriptor);
    }

    @Override
    public void processElement(Row value, Context ctx, Collector<Row> out) {
        try {
            String url = getStringFieldValue(value, urlFieldName);
            if (url == null || url.trim().isEmpty()) {
                log.debug("URL字段为空或空字符串，跳过处理");
                out.collect(value);
                return;
            }

            String urlKey = generateUrlKey(url);
            DimensionProcessor.updateTimeStateMap(urlDimensionState, urlKey);

            ctx.output(URL_DIM_TAG, createDimensionRow(urlKey, url));
            ctx.output(URL_VERTEX_TAG, createVertexRow(url));
            // 继续传递原始数据
            out.collect(value);
        } catch (Exception e) {
            log.error("处理URL维度数据时发生错误: {}", e.getMessage(), e);
            out.collect(value);
        }
    }

    /**
     * 创建符合维度表结构的URL维度记录
     *
     * @param urlKey URL键值
     * @param url    原始URL字符串
     * @return 符合dim_url表结构的Row
     */
    private Row createDimensionRow(String urlKey, String url) {
        Row dimensionRow = Row.withNames();
        dimensionRow.setField("id", urlKey);
        dimensionRow.setField("url", url);
        dimensionRow.setField("threat_score", 0);
        dimensionRow.setField("trust_score", 100);
        DimensionProcessor.insertTimeStateMap(urlDimensionState, urlKey, dimensionRow);

        return dimensionRow;
    }

    /**
     * 创建符合URL结构的记录
     *
     * @param url    原始URL字符串
     * @return 符合URL TAG结构的Row
     */
    private Row createVertexRow(String url) {
        return Row.of(url, // vid
                url, 0, 100
        );
    }

    /**
     * 生成URL键值
     *
     * @param url URL字符串
     * @return URL键值
     */
    private String generateUrlKey(String url) {
        return UUID.nameUUIDFromBytes(url.getBytes()).toString();
    }

    /**
     * 从Row中获取字符串字段值
     *
     * @param row       Row对象
     * @param fieldName 字段名
     * @return 字段值，如果不存在或类型不匹配则返回null
     */
    private String getStringFieldValue(Row row, String fieldName) {
        try {
            Set<String> fieldNames = row.getFieldNames(true);
            if (fieldNames != null && fieldNames.contains(fieldName)) {
                Object fieldValue = row.getField(fieldName);
                if (fieldValue instanceof String) {
                    return (String) fieldValue;
                }
            }
        } catch (Exception e) {
            log.warn("获取字符串字段值时发生错误，字段名: {}, 错误: {}", fieldName, e.getMessage());
        }
        return null;
    }
}
