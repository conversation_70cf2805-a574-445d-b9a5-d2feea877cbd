package com.geeksec.nta.trafficetl.etl.dim;

import com.geeksec.common.toolkit.time.TimeUtils;
import com.geeksec.nta.trafficetl.etl.constant.FieldConstants;
import com.geeksec.nta.trafficetl.etl.dim.function.*;
//import com.geeksec.nta.trafficetl.sink.NebulaSinkWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.doris.flink.sink.DorisSink;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.types.Row;

import java.time.LocalDateTime;
import java.util.Map;

import static com.geeksec.common.toolkit.time.TimeUtils.DEFAULT_DATE_TIME_FORMAT;
import static com.geeksec.common.toolkit.time.TimeUtils.DORIS_DATETIME_FORMATTER;

/**
 * Processor for handling dimension data extraction and writing to <PERSON>.
 * This class coordinates the extraction of dimension data from protocol metadata
 * and routes it to the appropriate Doris tables.
 *
 * <AUTHOR>
 */
@Slf4j
public class DimensionProcessor {

    /**
     * Process DNS metadata to extract dimension data and write to Doris
     *
     * @param dnsStream           The DNS metadata stream
     * @param domainDimensionSink Sink for domain dimension data
     * @param ipv4DimensionSink   Sink for IPv4 dimension data
     * @param ipv6DimensionSink   Sink for IPv6 dimension data
     * @param parallelism         The parallelism for sink operations
     * @return The processed DNS stream with dimension data extracted
     */
    public static void processDnsDimensions(
            DataStream<Row> dnsStream,
            DorisSink<Row> domainDimensionSink,
            DorisSink<Row> registrableDomainDimensionSink,
            DorisSink<Row> ipv4DimensionSink,
            DorisSink<Row> ipv6DimensionSink,
            int parallelism) {

        log.info("设置DNS维度数据提取");

        // 域名维度表处理函数
        domainDimensionInfo2Doris(dnsStream, "dns", domainDimensionSink, parallelism, registrableDomainDimensionSink);
        // IP维度表处理函数
        ipDimensionInfo2Doris(dnsStream, ipv4DimensionSink, ipv6DimensionSink, parallelism, FieldConstants.FIELD_DNS_DOMAIN_IP);

    }

    /**
     * Process HTTP metadata to extract dimension data and write to Doris
     *
     * @param httpStream       The HTTP metadata stream
     * @param urlDimensionSink Sink for URL dimension data
     * @param parallelism      The parallelism for sink operations
     * @return The processed HTTP stream with dimension data extracted
     */
    public static SingleOutputStreamOperator<Row> processHttpDimensions(
            DataStream<Row> httpStream,
            DorisSink<Row> urlDimensionSink,
            DorisSink<Row> uaDimensionSink,
            DorisSink<Row> deviceDimensionSink,
            DorisSink<Row> osDimensionSink,
            int parallelism) {

        log.info("设置HTTP维度数据提取");

        // 应用URL维度表处理函数
        SingleOutputStreamOperator<Row> urlProcessedStream = httpStream
                .process(new UrlDimensionTableFunction(FieldConstants.FIELD_HTTP_URL))
                .name("HTTP URL维度表处理");

        // 输出URL维度数据到Doris
        urlProcessedStream
                .getSideOutput(UrlDimensionTableFunction.URL_DIM_TAG)
                .sinkTo(urlDimensionSink)
                .name("URL维度Doris Sink")
                .setParallelism(parallelism);

        // 应用UA解析维度表处理函数
        SingleOutputStreamOperator<Row> uaParseProcessedStream = httpStream
                .process(new UAPParseDimensionTableFunction())
                .name("HTTP UA OS Device维度表处理");
        // UA、OS、设备维度信息输出到Doris
        uaParseProcessedStream
                .getSideOutput(UAPParseDimensionTableFunction.UA_DIM_TAG)
                .sinkTo(uaDimensionSink)
                .name("UA维度Doris Sink")
                .setParallelism(parallelism);
        uaParseProcessedStream
                .getSideOutput(UAPParseDimensionTableFunction.OS_DIM_TAG)
                .sinkTo(osDimensionSink)
                .name("OS维度Doris Sink")
                .setParallelism(parallelism);
        uaParseProcessedStream
                .getSideOutput(UAPParseDimensionTableFunction.DEVICE_DIM_TAG)
                .sinkTo(deviceDimensionSink)
                .name("Device维度Doris Sink")
                .setParallelism(parallelism);

        return urlProcessedStream;
    }

    /**
     * Process SSL metadata to extract dimension data and write to Doris
     *
     * @param sslStream           The HTTP metadata stream
     * @param domainDimensionSink Sink for domain dimension data
     * @param fingerDimensionSink Sink for URL dimension data
     * @param parallelism         The parallelism for sink operations
     * @return The processed HTTP stream with dimension data extracted
     */
    public static SingleOutputStreamOperator<Row> processSSLDimensions(
            DataStream<Row> sslStream,
            DorisSink<Row> domainDimensionSink,
            DorisSink<Row> registrableDomainDimensionSink,
            DorisSink<Row> fingerDimensionSink,
            int parallelism) {

        log.info("设置SSL维度数据提取");
        // 域名维度表处理函数
        domainDimensionInfo2Doris(sslStream, "ssl", domainDimensionSink, parallelism, registrableDomainDimensionSink);

        // 指纹维度表处理函数
        SingleOutputStreamOperator<Row> fingerDimensionStream = sslStream
                .process(new FingerDimensionTableFunction())
                .name("SSl 指纹维度表处理");

        fingerDimensionStream.getSideOutput(FingerDimensionTableFunction.FINGER_DIM_TAG)
                .sinkTo(fingerDimensionSink)
                .name("SSl 指纹维度表写入Doris")
                .setParallelism(parallelism);

        return fingerDimensionStream;
    }


    /**
     * Process session metadata to extract dimension data and write to Doris
     *
     * @param sessionStream           The session metadata stream
     * @param ipv4DimensionSink       Sink for IPv4 dimension data
     * @param ipv6DimensionSink       Sink for IPv6 dimension data
     * @param macDimensionSink        Sink for MAC address dimension data
     * @param appServiceDimensionSink Sink for application service dimension data
     * @param appDimensionSink        Sink for application dimension data
     * @param parallelism             The parallelism for sink operations
     * @return The processed session stream with dimension data extracted
     */
    public static SingleOutputStreamOperator<Row> processSessionDimensions(
            DataStream<Row> sessionStream,
            DorisSink<Row> ipv4DimensionSink,
            DorisSink<Row> ipv6DimensionSink,
            DorisSink<Row> macDimensionSink,
            DorisSink<Row> appServiceDimensionSink,
            DorisSink<Row> appDimensionSink,
            int parallelism) {

        log.info("设置Session维度数据提取");

        // 处理IP维度
        ipDimensionInfo2Doris(sessionStream, ipv4DimensionSink, ipv6DimensionSink, parallelism, FieldConstants.FIELD_SRC_IP);
        ipDimensionInfo2Doris(sessionStream, ipv4DimensionSink, ipv6DimensionSink, parallelism, FieldConstants.FIELD_DST_IP);

        // 处理MAC维度
        SingleOutputStreamOperator<Row> macProcessedStream = sessionStream
                .process(new MacDimensionTableFunction(FieldConstants.FIELD_SMAC))
                .name("Session源MAC维度表处理");

        SingleOutputStreamOperator<Row> macDstProcessedStream = sessionStream
                .process(new MacDimensionTableFunction(FieldConstants.FIELD_DMAC))
                .name("Session目标MAC维度表处理");

        // 处理应用服务维度
        SingleOutputStreamOperator<Row> appServiceProcessedStream = sessionStream
                .process(new AppServiceDimensionTableFunction(FieldConstants.FIELD_APP_NAME))
                .name("Session应用服务维度表处理");

        // 处理应用维度
        SingleOutputStreamOperator<Row> appProcessedStream = sessionStream
                .process(new AppDimensionTableFunction("app_name"))
                .name("Session应用维度表处理");

        // 输出MAC维度数据到Doris
        DataStream<Row> macStream = macProcessedStream
                .getSideOutput(MacDimensionTableFunction.MAC_DIM_TAG)
                .union(macDstProcessedStream.getSideOutput(MacDimensionTableFunction.MAC_DIM_TAG));

        macStream
                .sinkTo(macDimensionSink)
                .name("MAC维度Doris Sink")
                .setParallelism(parallelism);

        // 输出应用服务维度数据到Doris
        appServiceProcessedStream
                .getSideOutput(AppServiceDimensionTableFunction.APP_SERVICE_DIM_TAG)
                .sinkTo(appServiceDimensionSink)
                .name("应用服务维度Doris Sink")
                .setParallelism(parallelism);

        // 输出应用服务维度数据到Doris
        appProcessedStream
                .getSideOutput(AppDimensionTableFunction.APP_DIM_TAG)
                .sinkTo(appDimensionSink)
                .name("应用维度Doris Sink")
                .setParallelism(parallelism);
        // 输出应用顶点流到Nebula
//        NebulaSinkWrapper.createAppVertexSink(appProcessedStream
//                .getSideOutput(AppDimensionTableFunction.APP_VERTEX_TAG));

        return appProcessedStream;
    }

    /**
     * 从元数据中提取IP并转换为维度数据输出到维度表
     *
     * @param dataStream
     * @param ipv4DimensionSink
     * @param ipv6DimensionSink
     * @param parallelism
     * @param ipFieldName       IP字段名称
     */
    private static void ipDimensionInfo2Doris(DataStream<Row> dataStream, DorisSink<Row> ipv4DimensionSink, DorisSink<Row> ipv6DimensionSink, int parallelism, String ipFieldName) {
        // 应用IP维度表处理函数
        SingleOutputStreamOperator<Row> processedStream = dataStream
                .process(new IpDimensionTableFunction(ipFieldName))
                .name("IP维度表处理");

        // 输出IPv4维度数据到Doris
        processedStream
                .getSideOutput(IpDimensionTableFunction.IPV4_DIM_TAG)
                .sinkTo(ipv4DimensionSink)
                .name("IPv4维度Doris Sink")
                .setParallelism(parallelism);

        // 输出IPv6维度数据到Doris
        processedStream
                .getSideOutput(IpDimensionTableFunction.IPV6_DIM_TAG)
                .sinkTo(ipv6DimensionSink)
                .name("IPv6维度Doris Sink")
                .setParallelism(parallelism);
    }

    /**
     * 从元数据中提取域名并转换为维度数据输出到维度表
     *
     * @param dataStream
     * @param type                           元数据类型
     * @param domainDimensionSink
     * @param parallelism
     * @param registrableDomainDimensionSink
     */
    private static void domainDimensionInfo2Doris(DataStream<Row> dataStream, String type, DorisSink<Row> domainDimensionSink, int parallelism, DorisSink<Row> registrableDomainDimensionSink) {
        // 应用域名维度表处理函数
        SingleOutputStreamOperator<Row> domainProcessedStream = dataStream
                .process(new DomainDimensionTableFunction(type))
                .name(type + " 域名维度表处理");

        // 输出域名维度数据到Doris
        domainProcessedStream
                .getSideOutput(DomainDimensionTableFunction.DOMAIN_DIM_TAG)
                .sinkTo(domainDimensionSink)
                .name(type + " 域名维度Doris Sink")
                .setParallelism(parallelism);

        // 输出锚域名维度数据到Doris
        domainProcessedStream
                .getSideOutput(DomainDimensionTableFunction.REGISTRABLE_DOMAIN_DIM_TAG)
                .sinkTo(registrableDomainDimensionSink)
                .name(type + " 锚域名维度Doris Sink")
                .setParallelism(parallelism);
    }

    /**
     * 记录更新时间状态数据到MapState
     * @param mapState
     * @param key
     */
    public static void updateTimeStateMap(MapState<String, Map<String, Object>> mapState, String key){
        try {
            // 检查状态中是否已存在该应用的维度数据
            Map<String, Object> existingAppInfo = mapState.get(key);
            if (existingAppInfo == null) {
                // 首次遇到该应用，创建维度数据
                mapState.put(key, Map.of(
                        FieldConstants.KEY_DW_CREATION_TIMESTAMP, LocalDateTime.now().format(DORIS_DATETIME_FORMATTER),
                        FieldConstants.KEY_DW_LAST_UPDATED_TIMESTAMP, LocalDateTime.now().format(DORIS_DATETIME_FORMATTER)));
            } else {
                // 更新维度数据
                mapState.get(key).put(FieldConstants.KEY_DW_LAST_UPDATED_TIMESTAMP, LocalDateTime.now().format(DORIS_DATETIME_FORMATTER));
            }
        } catch (Exception e) {
            log.error("更新时间状态数据时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 从MapState提取时间状态并插入到维度数据中
     * @param mapState
     * @param key
     * @param row
     */
    public static void insertTimeStateMap(MapState<String, Map<String, Object>> mapState, String key, Row row){
        try {
            String updateTime = mapState.get(key).get(FieldConstants.KEY_DW_LAST_UPDATED_TIMESTAMP).toString();
            String currentTime = mapState.get(key).get(FieldConstants.KEY_DW_CREATION_TIMESTAMP).toString();
            row.setField("first_seen", currentTime);
            row.setField("last_seen", updateTime);
            row.setField("create_time", currentTime);
            row.setField("update_time", updateTime);
        } catch (Exception e) {
            log.error("插入时间状态数据时发生错误: {}", e.getMessage(), e);
            row.setField("first_seen", TimeUtils.format(LocalDateTime.now(), DEFAULT_DATE_TIME_FORMAT));
            row.setField("last_seen", TimeUtils.format(LocalDateTime.now(), DEFAULT_DATE_TIME_FORMAT));
            row.setField("create_time", TimeUtils.format(LocalDateTime.now(), DEFAULT_DATE_TIME_FORMAT));
            row.setField("update_time", TimeUtils.format(LocalDateTime.now(), DEFAULT_DATE_TIME_FORMAT));
        }
    }
}
