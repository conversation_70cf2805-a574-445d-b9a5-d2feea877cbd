package com.geeksec.nta.datawarehouse.etl.constant;

/**
 * 字段名常量定义
 *
 * <AUTHOR>
 */
public final class FieldConstants {

    /** 会话相关字段 */
    /**
     * session_id字段
     */
    public static final String FIELD_SESSION_ID = "session_id";
    /**
     * src_ip字段
     */
    public static final String FIELD_SRC_IP = "src_ip";
    /**
     * dst_ip字段
     */
    public static final String FIELD_DST_IP = "dst_ip";
    /**
     * src_port字段
     */
    public static final String FIELD_SRC_PORT = "src_port";
    /**
     * dst_port字段
     */
    public static final String FIELD_DST_PORT = "dst_port";
    /**
     * ippro字段
     */
    public static final String FIELD_IP_PROTOCOL = "ippro";
    /**
     * begin_time字段
     */
    public static final String FIELD_BEGIN_TIME = "begin_time";
    /**
     * begin_nsec字段
     */
    public static final String FIELD_BEGIN_NSEC = "begin_nsec";
    /**
     * end_time字段
     */
    public static final String FIELD_END_TIME = "end_time";
    /**
     * end_nsec字段
     */
    public static final String FIELD_END_NSEC = "end_nsec";
    /**
     * server_ip字段
     */
    public static final String FIELD_SERVER_IP = "server_ip";
    /**
     * app_id字段
     */
    public static final String FIELD_APP_ID = "app_id";
    /**
     * app_name字段
     */
    public static final String FIELD_APP_NAME = "app_name";
    /**
     * thread_id字段
     */
    public static final String FIELD_THREAD_ID = "thread_id";
    /**
     * task_id字段
     */
    public static final String FIELD_TASK_ID = "task_id";
    /**
     * batch_id字段
     */
    public static final String FIELD_BATCH_ID = "batch_id";

    /** MAC地址相关 */
    /**
     * smac字段
     */
    public static final String FIELD_SMAC = "smac";
    /**
     * dmac字段
     */
    public static final String FIELD_DMAC = "dmac";

    /** 会话统计信息 */
    /**
     * pkt_sbytes字段
     */
    public static final String FIELD_PKT_SBYTES = "pkt_sbytes";
    /**
     * pkt_dbytes字段
     */
    public static final String FIELD_PKT_DBYTES = "pkt_dbytes";
    /**
     * pkt_snum字段
     */
    public static final String FIELD_PKT_SNUM = "pkt_snum";
    /**
     * pkt_dnum字段
     */
    public static final String FIELD_PKT_DNUM = "pkt_dnum";
    /**
     * duration字段
     */
    public static final String FIELD_DURATION = "duration";

    /** 其他通用字段 */
    /**
     * create_time字段
     */
    public static final String FIELD_CREATE_TIME = "create_time";
    /**
     * domain字段
     */
    public static final String FIELD_DOMAIN = "domain";
    /**
     * domain_ip字段
     */
    public static final String FIELD_DOMAIN_IP = "domain_ip";

    /** 设备相关 */
    /**
     * device_type字段
     */
    public static final String FIELD_DEVICE_TYPE = "device_type";
    /**
     * device_id字段
     */
    public static final String FIELD_DEVICE_ID = "device_id";
    /**
     * os_name字段
     */
    public static final String FIELD_OS_NAME = "os_name";

    /** 会话统计信息 */
    /**
     * rule_num字段
     */
    public static final String FIELD_RULE_NUM = "rule_num";
    /**
     * rule_level字段
     */
    public static final String FIELD_RULE_LEVEL = "rule_level";
    /**
     * syn字段
     */
    public static final String FIELD_SYN = "syn";
    /**
     * syn_ack字段
     */
    public static final String FIELD_SYN_ACK = "syn_ack";
    /**
     * rule_msg字段
     */
    public static final String FIELD_RULE_MSG = "rule_msg";
    /**
     * rule_labels字段
     */
    public static final String FIELD_RULE_LABELS = "rule_labels";
    /**
     * port_list字段
     */
    public static final String FIELD_PORT_LIST = "port_list";
    /**
     * first_proto字段
     */
    public static final String FIELD_FIRST_PROTO = "first_proto";
    /**
     * first_sender字段
     */
    public static final String FIELD_FIRST_SENDER = "first_sender";

    /** 代理相关 */
    /**
     * proxy_ip字段
     */
    public static final String FIELD_PROXY_IP = "proxy_ip";
    /**
     * proxy_port字段
     */
    public static final String FIELD_PROXY_PORT = "proxy_port";
    /**
     * proxy_real_host字段
     */
    public static final String FIELD_PROXY_REAL_HOST = "proxy_real_host";
    /**
     * proxy_type字段
     */
    public static final String FIELD_PROXY_TYPE = "proxy_type";

    /** 处理时间 */
    /**
     * handle_begin_time字段
     */
    public static final String FIELD_HANDLE_BEGIN_TIME = "handle_begin_time";
    /**
     * handle_end_time字段
     */
    public static final String FIELD_HANDLE_END_TIME = "handle_end_time";

    /** 会话统计 */
    /**
     * stats_stotalsign字段
     */
    public static final String FIELD_STATS_STOTALSIGN = "stats_stotalsign";
    /**
     * stats_dtotalsign字段
     */
    public static final String FIELD_STATS_DTOTALSIGN = "stats_dtotalsign";
    /**
     * stats_distbytes字段
     */
    public static final String FIELD_STATS_DISTBYTES = "stats_distbytes";
    /**
     * stats_distbytesnum字段
     */
    public static final String FIELD_STATS_DISTBYTESNUM = "stats_distbytesnum";
    /**
     * stats_distcsq字段
     */
    public static final String FIELD_STATS_DISTCSQ = "stats_distcsq";
    /**
     * stats_distcsqt字段
     */
    public static final String FIELD_STATS_DISTCSQT = "stats_distcsqt";
    /**
     * stats_sdistlen字段
     */
    public static final String FIELD_STATS_SDISTLEN = "stats_sdistlen";
    /**
     * stats_ddistlen字段
     */
    public static final String FIELD_STATS_DDISTLEN = "stats_ddistlen";
    /**
     * stats_distdur字段
     */
    public static final String FIELD_STATS_DISTDUR = "stats_distdur";
    /**
     * stats_prolist_num字段
     */
    public static final String FIELD_STATS_PROLIST_NUM = "stats_prolist_num";
    /**
     * stats_prolist字段
     */
    public static final String FIELD_STATS_PROLIST = "stats_prolist";
    /**
     * sio_sign字段
     */
    public static final String FIELD_SIO_SIGN = "sio_sign";
    /**
     * dio_sign字段
     */
    public static final String FIELD_DIO_SIGN = "dio_sign";
    /**
     * ext_json字段
     */
    public static final String FIELD_EXT_JSON = "ext_json";
    /**
     * stats_tcp_info字段
     */
    public static final String FIELD_STATS_TCP_INFO = "stats_tcp_info";

    /** TCP统计信息 */
    /**
     * stats_src_mss字段
     */
    public static final String FIELD_STATS_SRC_MSS = "stats_src_mss";
    /**
     * stats_dst_mss字段
     */
    public static final String FIELD_STATS_DST_MSS = "stats_dst_mss";
    /**
     * stats_src_window_scale字段
     */
    public static final String FIELD_STATS_SRC_WINDOW_SCALE = "stats_src_window_scale";
    /**
     * stats_dst_window_scale字段
     */
    public static final String FIELD_STATS_DST_WINDOW_SCALE = "stats_dst_window_scale";
    /**
     * stats_spayload_maxlen字段
     */
    public static final String FIELD_STATS_SPAYLOAD_MAXLEN = "stats_spayload_maxlen";
    /**
     * stats_dpayload_maxlen字段
     */
    public static final String FIELD_STATS_DPAYLOAD_MAXLEN = "stats_dpayload_maxlen";
    /**
     * stats_sack_payload_maxlen字段
     */
    public static final String FIELD_STATS_SACK_PAYLOAD_MAXLEN = "stats_sack_payload_maxlen";
    /**
     * stats_dack_payload_maxlen字段
     */
    public static final String FIELD_STATS_DACK_PAYLOAD_MAXLEN = "stats_dack_payload_maxlen";
    /**
     * stats_sack_payload_minlen字段
     */
    public static final String FIELD_STATS_SACK_PAYLOAD_MINLEN = "stats_sack_payload_minlen";
    /**
     * stats_dack_payload_minlen字段
     */
    public static final String FIELD_STATS_DACK_PAYLOAD_MINLEN = "stats_dack_payload_minlen";
    /**
     * syn_seq字段
     */
    public static final String FIELD_SYN_SEQ = "syn_seq";
    /**
     * syn_seq_num字段
     */
    public static final String FIELD_SYN_SEQ_NUM = "syn_seq_num";
    /**
     * stats_sipid_offset字段
     */
    public static final String FIELD_STATS_SIPID_OFFSET = "stats_sipid_offset";
    /**
     * stats_dipid_offset字段
     */
    public static final String FIELD_STATS_DIPID_OFFSET = "stats_dipid_offset";
    /**
     * block_cipher字段
     */
    public static final String FIELD_BLOCK_CIPHER = "block_cipher";

    /** 包信息 */
    /**
     * pkt_infor字段
     */
    public static final String FIELD_PKT_INFOR = "pkt_infor";
    /**
     * pkt_smaxlen字段
     */
    public static final String FIELD_PKT_SMAXLEN = "pkt_smaxlen";
    /**
     * pkt_dmaxlen字段
     */
    public static final String FIELD_PKT_DMAXLEN = "pkt_dmaxlen";
    /**
     * pkt_spayloadnum字段
     */
    public static final String FIELD_PKT_SPAYLOADNUM = "pkt_spayloadnum";
    /**
     * pkt_dpayloadnum字段
     */
    public static final String FIELD_PKT_DPAYLOADNUM = "pkt_dpayloadnum";
    /**
     * pkt_spayloadbytes字段
     */
    public static final String FIELD_PKT_SPAYLOADBYTES = "pkt_spayloadbytes";
    /**
     * pkt_dpayloadbytes字段
     */
    public static final String FIELD_PKT_DPAYLOADBYTES = "pkt_dpayloadbytes";
    /**
     * pkt_sfinnum字段
     */
    public static final String FIELD_PKT_SFINNUM = "pkt_sfinnum";
    /**
     * pkt_dfinnum字段
     */
    public static final String FIELD_PKT_DFINNUM = "pkt_dfinnum";
    /**
     * pkt_srstnum字段
     */
    public static final String FIELD_PKT_SRSTNUM = "pkt_srstnum";
    /**
     * pkt_drstnum字段
     */
    public static final String FIELD_PKT_DRSTNUM = "pkt_drstnum";
    /**
     * pkt_ssynnum字段
     */
    public static final String FIELD_PKT_SSYNNUM = "pkt_ssynnum";
    /**
     * pkt_dsynnum字段
     */
    public static final String FIELD_PKT_DSYNNUM = "pkt_dsynnum";
    /**
     * pkt_ssynbytes字段
     */
    public static final String FIELD_PKT_SSYNBYTES = "pkt_ssynbytes";
    /**
     * pkt_dsynbytes字段
     */
    public static final String FIELD_PKT_DSYNBYTES = "pkt_dsynbytes";
    /**
     * pkt_sttlmax字段
     */
    public static final String FIELD_PKT_STTLMAX = "pkt_sttlmax";
    /**
     * pkt_dttlmax字段
     */
    public static final String FIELD_PKT_DTTLMAX = "pkt_dttlmax";
    /**
     * pkt_sttlmin字段
     */
    public static final String FIELD_PKT_STTLMIN = "pkt_sttlmin";
    /**
     * pkt_dttlmin字段
     */
    public static final String FIELD_PKT_DTTLMIN = "pkt_dttlmin";
    /**
     * pkt_sdurmax字段
     */
    public static final String FIELD_PKT_SDURMAX = "pkt_sdurmax";
    /**
     * pkt_ddurmax字段
     */
    public static final String FIELD_PKT_DDURMAX = "pkt_ddurmax";
    /**
     * pkt_sdurmin字段
     */
    public static final String FIELD_PKT_SDURMIN = "pkt_sdurmin";
    /**
     * pkt_ddurmin字段
     */
    public static final String FIELD_PKT_DDURMIN = "pkt_ddurmin";
    /**
     * pkt_sdisorder字段
     */
    public static final String FIELD_PKT_SDISORDER = "pkt_sdisorder";
    /**
     * pkt_ddisorder字段
     */
    public static final String FIELD_PKT_DDISORDER = "pkt_ddisorder";
    /**
     * pkt_sresend字段
     */
    public static final String FIELD_PKT_SRESEND = "pkt_sresend";
    /**
     * pkt_dresend字段
     */
    public static final String FIELD_PKT_DRESEND = "pkt_dresend";
    /**
     * pkt_slost字段
     */
    public static final String FIELD_PKT_SLOST = "pkt_slost";
    /**
     * pkt_dlost字段
     */
    public static final String FIELD_PKT_DLOST = "pkt_dlost";
    /**
     * pkt_spshnum字段
     */
    public static final String FIELD_PKT_SPSHNUM = "pkt_spshnum";
    /**
     * pkt_dpshnum字段
     */
    public static final String FIELD_PKT_DPSHNUM = "pkt_dpshnum";
    /**
     * pkt_pronum字段
     */
    public static final String FIELD_PKT_PRONUM = "pkt_pronum";
    /**
     * pkt_unkonw_pronum字段
     */
    public static final String FIELD_PKT_UNKONW_PRONUM = "pkt_unkonw_pronum";
    /**
     * pkt_syn_data字段
     */
    public static final String FIELD_PKT_SYN_DATA = "pkt_syn_data";
    /**
     * pkt_sbadnum字段
     */
    public static final String FIELD_PKT_SBADNUM = "pkt_sbadnum";
    /**
     * pkt_dbadnum字段
     */
    public static final String FIELD_PKT_DBADNUM = "pkt_dbadnum";
    /**
     * app_pkt_id字段
     */
    public static final String FIELD_APP_PKT_ID = "app_pkt_id";
    /**
     * pkt_spayload字段
     */
    public static final String FIELD_PKT_SPAYLOAD = "pkt_spayload";
    /**
     * pkt_dpayload字段
     */
    public static final String FIELD_PKT_DPAYLOAD = "pkt_dpayload";

    /** TCP指纹 */
    /**
     * tcp_c_finger字段
     */
    public static final String FIELD_TCP_C_FINGER = "tcp_c_finger";
    /**
     * tcp_s_finger字段
     */
    public static final String FIELD_TCP_S_FINGER = "tcp_s_finger";
    /**
     * http_c_finger字段
     */
    public static final String FIELD_HTTP_C_FINGER = "http_c_finger";
    /**
     * http_s_finger字段
     */
    public static final String FIELD_HTTP_S_FINGER = "http_s_finger";
    /**
     * ssl_c_finger字段
     */
    public static final String FIELD_SSL_C_FINGER = "ssl_c_finger";
    /**
     * ssl_s_finger字段
     */
    public static final String FIELD_SSL_S_FINGER = "ssl_s_finger";

    /** TCP特性 */
    /**
     * tcp_c_feature_ecn_ip_ect字段
     */
    public static final String FIELD_TCP_C_FEATURE_ECN_IP_ECT = "tcp_c_feature_ecn_ip_ect";
    /**
     * tcp_c_feature_qk_dfnz_ipid字段
     */
    public static final String FIELD_TCP_C_FEATURE_QK_DFNZ_IPID = "tcp_c_feature_qk_dfnz_ipid";
    /**
     * tcp_c_feature_flag_cwr字段
     */
    public static final String FIELD_TCP_C_FEATURE_FLAG_CWR = "tcp_c_feature_flag_cwr";
    /**
     * tcp_c_feature_flag_ece字段
     */
    public static final String FIELD_TCP_C_FEATURE_FLAG_ECE = "tcp_c_feature_flag_ece";
    public static final String FIELD_TCP_C_FEATURE_QK_OPT_ZERO_TS1 = "tcp_c_feature_qk_opt_zero_ts1";
    /**
     * tcp_c_feature_ttl字段
     */
    public static final String FIELD_TCP_C_FEATURE_TTL = "tcp_c_feature_ttl";
    /**
     * tcp_c_feature_tcpopt_eol_padnum字段
     */
    public static final String FIELD_TCP_C_FEATURE_TCPOPT_EOL_PADNUM = "tcp_c_feature_tcpopt_eol_padnum";
    /**
     * tcp_c_feature_tcpopt_wscale字段
     */
    public static final String FIELD_TCP_C_FEATURE_TCPOPT_WSCALE = "tcp_c_feature_tcpopt_wscale";
    /**
     * tcp_c_feature_qk_win_mss字段
     */
    public static final String FIELD_TCP_C_FEATURE_QK_WIN_MSS = "tcp_c_feature_qk_win_mss";
    /**
     * tcp_c_feature_tcpopt_layout字段
     */
    public static final String FIELD_TCP_C_FEATURE_TCPOPT_LAYOUT = "tcp_c_feature_tcpopt_layout";
    /**
     * tcp_s_feature_ecn_ip_ect字段
     */
    public static final String FIELD_TCP_S_FEATURE_ECN_IP_ECT = "tcp_s_feature_ecn_ip_ect";
    /**
     * tcp_s_feature_qk_dfnz_ipid字段
     */
    public static final String FIELD_TCP_S_FEATURE_QK_DFNZ_IPID = "tcp_s_feature_qk_dfnz_ipid";
    /**
     * tcp_s_feature_flag_cwr字段
     */
    public static final String FIELD_TCP_S_FEATURE_FLAG_CWR = "tcp_s_feature_flag_cwr";
    /**
     * tcp_s_feature_flag_ece字段
     */
    public static final String FIELD_TCP_S_FEATURE_FLAG_ECE = "tcp_s_feature_flag_ece";
    public static final String FIELD_TCP_S_FEATURE_QK_OPT_ZERO_TS1 = "tcp_s_feature_qk_opt_zero_ts1";
    /**
     * tcp_s_feature_ttl字段
     */
    public static final String FIELD_TCP_S_FEATURE_TTL = "tcp_s_feature_ttl";
    /**
     * tcp_s_feature_tcpopt_eol_padnum字段
     */
    public static final String FIELD_TCP_S_FEATURE_TCPOPT_EOL_PADNUM = "tcp_s_feature_tcpopt_eol_padnum";
    /**
     * tcp_s_feature_tcpopt_wscale字段
     */
    public static final String FIELD_TCP_S_FEATURE_TCPOPT_WSCALE = "tcp_s_feature_tcpopt_wscale";
    /**
     * tcp_s_feature_qk_win_mss字段
     */
    public static final String FIELD_TCP_S_FEATURE_QK_WIN_MSS = "tcp_s_feature_qk_win_mss";
    /** AUTH 相关字段 */
    /**
     * client_username字段
     */
    public static final String FIELD_CLIENT_USERNAME = "client_username";
    /**
     * passwd字段
     */
    public static final String FIELD_PASSWD = "passwd";
    /**
     * server_username字段
     */
    public static final String FIELD_SERVER_USERNAME = "server_username";

    /** CLIENT 相关字段 */
    /**
     * client_authentication_data字段
     */
    public static final String FIELD_CLIENT_AUTHENTICATION_DATA = "client_authentication_data";
    /**
     * client_authentication_names字段
     */
    public static final String FIELD_CLIENT_AUTHENTICATION_NAMES = "client_authentication_names";
    /**
     * client_authentication_response字段
     */
    public static final String FIELD_CLIENT_AUTHENTICATION_RESPONSE = "client_authentication_response";
    /**
     * client_authorization_data字段
     */
    public static final String FIELD_CLIENT_AUTHORIZATION_DATA = "client_authorization_data";
    /**
     * client_authorization_names字段
     */
    public static final String FIELD_CLIENT_AUTHORIZATION_NAMES = "client_authorization_names";
    /**
     * client_big_endian_flag字段
     */
    public static final String FIELD_CLIENT_BIG_ENDIAN_FLAG = "client_big_endian_flag";
    /**
     * client_bits_per_pixel字段
     */
    public static final String FIELD_CLIENT_BITS_PER_PIXEL = "client_bits_per_pixel";
    /**
     * client_blue_maximum字段
     */
    public static final String FIELD_CLIENT_BLUE_MAXIMUM = "client_blue_maximum";
    /**
     * client_blue_shift字段
     */
    public static final String FIELD_CLIENT_BLUE_SHIFT = "client_blue_shift";
    /**
     * client_build字段
     */
    public static final String FIELD_CLIENT_BUILD = "client_build";
    /**
     * client_clock_precision字段
     */
    public static final String FIELD_CLIENT_CLOCK_PRECISION = "client_clock_precision";
    /**
     * client_compression_algorithms_client_to_server字段
     */
    public static final String FIELD_CLIENT_COMPRESSION_ALGORITHMS_CLIENT_TO_SERVER = "client_compression_algorithms_client_to_server";
    /**
     * client_compression_algorithms_server_to_client字段
     */
    public static final String FIELD_CLIENT_COMPRESSION_ALGORITHMS_SERVER_TO_CLIENT = "client_compression_algorithms_server_to_client";
    /**
     * client_cookie字段
     */
    public static final String FIELD_CLIENT_COOKIE = "client_cookie";
    /**
     * client_depth字段
     */
    public static final String FIELD_CLIENT_DEPTH = "client_depth";
    /**
     * client_encryption_algorithms_client_to_server字段
     */
    public static final String FIELD_CLIENT_ENCRYPTION_ALGORITHMS_CLIENT_TO_SERVER = "client_encryption_algorithms_client_to_server";
    /**
     * client_encryption_algorithms_server_to_client字段
     */
    public static final String FIELD_CLIENT_ENCRYPTION_ALGORITHMS_SERVER_TO_CLIENT = "client_encryption_algorithms_server_to_client";
    /**
     * client_green_maximum字段
     */
    public static final String FIELD_CLIENT_GREEN_MAXIMUM = "client_green_maximum";
    /**
     * client_green_shift字段
     */
    public static final String FIELD_CLIENT_GREEN_SHIFT = "client_green_shift";
    /**
     * client_kex_algorithms字段
     */
    public static final String FIELD_CLIENT_KEX_ALGORITHMS = "client_kex_algorithms";
    /**
     * client_mac_algorithms_client_to_server字段
     */
    public static final String FIELD_CLIENT_MAC_ALGORITHMS_CLIENT_TO_SERVER = "client_mac_algorithms_client_to_server";
    /**
     * client_mac_algorithms_server_to_client字段
     */
    public static final String FIELD_CLIENT_MAC_ALGORITHMS_SERVER_TO_CLIENT = "client_mac_algorithms_server_to_client";
    /**
     * client_name字段
     */
    public static final String FIELD_CLIENT_NAME = "client_name";
    /**
     * client_origin_ts_nsec字段
     */
    public static final String FIELD_CLIENT_ORIGIN_TS_NSEC = "client_origin_ts_nsec";
    /**
     * client_origin_ts_sec字段
     */
    public static final String FIELD_CLIENT_ORIGIN_TS_SEC = "client_origin_ts_sec";
    /**
     * client_poll_interval_sec字段
     */
    public static final String FIELD_CLIENT_POLL_INTERVAL_SEC = "client_poll_interval_sec";
    /**
     * client_productid字段
     */
    public static final String FIELD_CLIENT_PRODUCTID = "client_productid";
    /**
     * client_protocol字段
     */
    public static final String FIELD_CLIENT_PROTOCOL = "client_protocol";
    /**
     * client_protocol_ver字段
     */
    public static final String FIELD_CLIENT_PROTOCOL_VER = "client_protocol_ver";
    /**
     * client_recv_ts_nsec字段
     */
    public static final String FIELD_CLIENT_RECV_TS_NSEC = "client_recv_ts_nsec";
    /**
     * client_recv_ts_sec字段
     */
    public static final String FIELD_CLIENT_RECV_TS_SEC = "client_recv_ts_sec";
    /**
     * client_red_maximum字段
     */
    public static final String FIELD_CLIENT_RED_MAXIMUM = "client_red_maximum";
    /**
     * client_red_shift字段
     */
    public static final String FIELD_CLIENT_RED_SHIFT = "client_red_shift";
    /**
     * client_refer_ts_nsec字段
     */
    public static final String FIELD_CLIENT_REFER_TS_NSEC = "client_refer_ts_nsec";
    /**
     * client_refer_ts_sec字段
     */
    public static final String FIELD_CLIENT_REFER_TS_SEC = "client_refer_ts_sec";
    /**
     * client_reference_identifier字段
     */
    public static final String FIELD_CLIENT_REFERENCE_IDENTIFIER = "client_reference_identifier";
    /**
     * client_root_delay字段
     */
    public static final String FIELD_CLIENT_ROOT_DELAY = "client_root_delay";
    /**
     * client_root_dispersion字段
     */
    public static final String FIELD_CLIENT_ROOT_DISPERSION = "client_root_dispersion";
    /**
     * client_secure_type_selected字段
     */
    public static final String FIELD_CLIENT_SECURE_TYPE_SELECTED = "client_secure_type_selected";
    /**
     * client_server_host_key_algorithms字段
     */
    public static final String FIELD_CLIENT_SERVER_HOST_KEY_ALGORITHMS = "client_server_host_key_algorithms";
    /**
     * client_stratum字段
     */
    public static final String FIELD_CLIENT_STRATUM = "client_stratum";
    /**
     * client_true_color_flag字段
     */
    public static final String FIELD_CLIENT_TRUE_COLOR_FLAG = "client_true_color_flag";
    /**
     * client_version_major字段
     */
    public static final String FIELD_CLIENT_VERSION_MAJOR = "client_version_major";
    /**
     * client_version_minor字段
     */
    public static final String FIELD_CLIENT_VERSION_MINOR = "client_version_minor";
    /**
     * client_xmit_ts_nsec字段
     */
    public static final String FIELD_CLIENT_XMIT_TS_NSEC = "client_xmit_ts_nsec";
    /**
     * client_xmit_ts_sec字段
     */
    public static final String FIELD_CLIENT_XMIT_TS_SEC = "client_xmit_ts_sec";

    /** DNS 相关字段 */
    /**
     * dns_add字段
     */
    public static final String FIELD_DNS_ADD = "dns_add";
    /**
     * dns_ans字段
     */
    public static final String FIELD_DNS_ANS = "dns_ans";
    /**
     * dns_answer字段
     */
    public static final String FIELD_DNS_ANSWER = "dns_answer";
    /**
     * dns_auth字段
     */
    public static final String FIELD_DNS_AUTH = "dns_auth";
    /**
     * dns_domain字段
     */
    public static final String FIELD_DNS_DOMAIN = "dns_domain";
    /**
     * dns_domain_ip字段
     */
    public static final String FIELD_DNS_DOMAIN_IP = "dns_domain_ip";
    /**
     * dns_flags字段
     */
    public static final String FIELD_DNS_FLAGS = "dns_flags";
    /**
     * dns_id字段
     */
    public static final String FIELD_DNS_ID = "dns_id";
    /**
     * dns_que字段
     */
    public static final String FIELD_DNS_QUE = "dns_que";
    /**
     * dns_query字段
     */
    public static final String FIELD_DNS_QUERY = "dns_query";

    /** HTTP 相关字段 */
    /**
     * act字段
     */
    public static final String FIELD_HTTP_ACT = "act";
    /**
     * http_client_kv字段
     */
    public static final String FIELD_HTTP_CLIENT_KV = "http_client_kv";
    /**
     * http_client_title字段
     */
    public static final String FIELD_HTTP_CLIENT_TITLE = "http_client_title";
    /**
     * host字段
     */
    public static final String FIELD_HTTP_HOST = "host";
    /**
     * response字段
     */
    public static final String FIELD_HTTP_RESPONSE = "response";
    /**
     * http_server_kv字段
     */
    public static final String FIELD_HTTP_SERVER_KV = "http_server_kv";
    /**
     * http_server_title字段
     */
    public static final String FIELD_HTTP_SERVER_TITLE = "http_server_title";
    /**
     * url字段
     */
    public static final String FIELD_HTTP_URL = "url";
/**
     * user_agent字段
     */
    public static final String FIELD_HTTP_UA = "user_agent";

    /** ICMP 相关字段 */
    /**
     * data_con字段
     */
    public static final String FIELD_DATA_CON = "data_con";
    /**
     * echo_seq_num字段
     */
    public static final String FIELD_ECHO_SEQ_NUM = "echo_seq_num";
    /**
     * info_code字段
     */
    public static final String FIELD_INFO_CODE = "info_code";
    /**
     * msg_type字段
     */
    public static final String FIELD_MSG_TYPE = "msg_type";
    /**
     * ver字段
     */
    public static final String FIELD_VER = "ver";

    /** RDP_DISPLAY 相关字段 */
    /**
     * desktop_height字段
     */
    public static final String FIELD_DESKTOP_HEIGHT = "desktop_height";
    /**
     * desktop_name字段
     */
    public static final String FIELD_DESKTOP_NAME = "desktop_name";
    /**
     * desktop_width字段
     */
    public static final String FIELD_DESKTOP_WIDTH = "desktop_width";
    /**
     * keyboard_funckey字段
     */
    public static final String FIELD_KEYBOARD_FUNCKEY = "keyboard_funckey";
    /**
     * keyboard_layout字段
     */
    public static final String FIELD_KEYBOARD_LAYOUT = "keyboard_layout";
    /**
     * keyboard_subtype字段
     */
    public static final String FIELD_KEYBOARD_SUBTYPE = "keyboard_subtype";
    /**
     * keyboard_type字段
     */
    public static final String FIELD_KEYBOARD_TYPE = "keyboard_type";

    /** SECURITY 相关字段 */
    /**
     * encryption_level字段
     */
    public static final String FIELD_ENCRYPTION_LEVEL = "encryption_level";
    /**
     * encryption_method字段
     */
    public static final String FIELD_ENCRYPTION_METHOD = "encryption_method";
    /**
     * encryption_methods字段
     */
    public static final String FIELD_ENCRYPTION_METHODS = "encryption_methods";

    /** SERVER 相关字段 */
    /**
     * server_authentication_challenge字段
     */
    public static final String FIELD_SERVER_AUTHENTICATION_CHALLENGE = "server_authentication_challenge";
    /**
     * server_authentication_data字段
     */
    public static final String FIELD_SERVER_AUTHENTICATION_DATA = "server_authentication_data";
    /**
     * server_authentication_names字段
     */
    public static final String FIELD_SERVER_AUTHENTICATION_NAMES = "server_authentication_names";
    /**
     * server_authorization_data字段
     */
    public static final String FIELD_SERVER_AUTHORIZATION_DATA = "server_authorization_data";
    /**
     * server_authorization_names字段
     */
    public static final String FIELD_SERVER_AUTHORIZATION_NAMES = "server_authorization_names";
    /**
     * server_cert字段
     */
    public static final String FIELD_SERVER_CERT = "server_cert";
    /**
     * server_certlen字段
     */
    public static final String FIELD_SERVER_CERTLEN = "server_certlen";
    /**
     * server_channel_count字段
     */
    public static final String FIELD_SERVER_CHANNEL_COUNT = "server_channel_count";
    /**
     * server_clock_precision字段
     */
    public static final String FIELD_SERVER_CLOCK_PRECISION = "server_clock_precision";
    /**
     * server_compression_algorithms_client_to_server字段
     */
    public static final String FIELD_SERVER_COMPRESSION_ALGORITHMS_CLIENT_TO_SERVER = "server_compression_algorithms_client_to_server";
    /**
     * server_compression_algorithms_server_to_client字段
     */
    public static final String FIELD_SERVER_COMPRESSION_ALGORITHMS_SERVER_TO_CLIENT = "server_compression_algorithms_server_to_client";
    /**
     * server_cookie字段
     */
    public static final String FIELD_SERVER_COOKIE = "server_cookie";
    /**
     * server_encryption_algorithms_client_to_server字段
     */
    public static final String FIELD_SERVER_ENCRYPTION_ALGORITHMS_CLIENT_TO_SERVER = "server_encryption_algorithms_client_to_server";
    /**
     * server_encryption_algorithms_server_to_client字段
     */
    public static final String FIELD_SERVER_ENCRYPTION_ALGORITHMS_SERVER_TO_CLIENT = "server_encryption_algorithms_server_to_client";
    /**
     * server_kex_algorithms字段
     */
    public static final String FIELD_SERVER_KEX_ALGORITHMS = "server_kex_algorithms";
    /**
     * server_mac_algorithms_client_to_server字段
     */
    public static final String FIELD_SERVER_MAC_ALGORITHMS_CLIENT_TO_SERVER = "server_mac_algorithms_client_to_server";
    /**
     * server_mac_algorithms_server_to_client字段
     */
    public static final String FIELD_SERVER_MAC_ALGORITHMS_SERVER_TO_CLIENT = "server_mac_algorithms_server_to_client";
    /**
     * server_origin_ts_nsec字段
     */
    public static final String FIELD_SERVER_ORIGIN_TS_NSEC = "server_origin_ts_nsec";
    /**
     * server_origin_ts_sec字段
     */
    public static final String FIELD_SERVER_ORIGIN_TS_SEC = "server_origin_ts_sec";
    /**
     * server_poll_interval_sec字段
     */
    public static final String FIELD_SERVER_POLL_INTERVAL_SEC = "server_poll_interval_sec";
    /**
     * server_protocol字段
     */
    public static final String FIELD_SERVER_PROTOCOL = "server_protocol";
    /**
     * server_protocol_ver字段
     */
    public static final String FIELD_SERVER_PROTOCOL_VER = "server_protocol_ver";
    /**
     * server_random字段
     */
    public static final String FIELD_SERVER_RANDOM = "server_random";
    /**
     * server_randomlen字段
     */
    public static final String FIELD_SERVER_RANDOMLEN = "server_randomlen";
    /**
     * server_recv_ts_nsec字段
     */
    public static final String FIELD_SERVER_RECV_TS_NSEC = "server_recv_ts_nsec";
    /**
     * server_recv_ts_sec字段
     */
    public static final String FIELD_SERVER_RECV_TS_SEC = "server_recv_ts_sec";
    /**
     * server_refer_ts_nsec字段
     */
    public static final String FIELD_SERVER_REFER_TS_NSEC = "server_refer_ts_nsec";
    /**
     * server_refer_ts_sec字段
     */
    public static final String FIELD_SERVER_REFER_TS_SEC = "server_refer_ts_sec";
    /**
     * server_reference_identifier字段
     */
    public static final String FIELD_SERVER_REFERENCE_IDENTIFIER = "server_reference_identifier";
    /**
     * server_root_delay字段
     */
    public static final String FIELD_SERVER_ROOT_DELAY = "server_root_delay";
    /**
     * server_root_dispersion字段
     */
    public static final String FIELD_SERVER_ROOT_DISPERSION = "server_root_dispersion";
    /**
     * server_secure_type_supported字段
     */
    public static final String FIELD_SERVER_SECURE_TYPE_SUPPORTED = "server_secure_type_supported";
    /**
     * server_server_host_key_algorithms字段
     */
    public static final String FIELD_SERVER_SERVER_HOST_KEY_ALGORITHMS = "server_server_host_key_algorithms";
    /**
     * server_stratum字段
     */
    public static final String FIELD_SERVER_STRATUM = "server_stratum";
    /**
     * server_version_major字段
     */
    public static final String FIELD_SERVER_VERSION_MAJOR = "server_version_major";
    /**
     * server_version_minor字段
     */
    public static final String FIELD_SERVER_VERSION_MINOR = "server_version_minor";
    /**
     * server_xmit_ts_nsec字段
     */
    public static final String FIELD_SERVER_XMIT_TS_NSEC = "server_xmit_ts_nsec";
    /**
     * server_xmit_ts_sec字段
     */
    public static final String FIELD_SERVER_XMIT_TS_SEC = "server_xmit_ts_sec";

    // SSH 相关字段
    public static final String FIELD_SSH_KEY_FINGERPRINT_MD5_SERVER = "ssh_key_fingerprint_md5_server";

    /** SSL 相关字段 */
    /**
     * ssl_c_keyexchange字段
     */
    public static final String FIELD_SSL_C_KEYEXCHANGE = "ssl_c_keyexchange";
    /**
     * ssl_c_keyexchangelen字段
     */
    public static final String FIELD_SSL_C_KEYEXCHANGELEN = "ssl_c_keyexchangelen";
    /**
     * ssl_c_version字段
     */
    public static final String FIELD_SSL_C_VERSION = "ssl_c_version";
    /**
     * ssl_cert_c_hash字段
     */
    public static final String FIELD_SSL_CERT_C_HASH = "ssl_cert_c_hash";
    /**
     * ssl_cert_c_num字段
     */
    public static final String FIELD_SSL_CERT_C_NUM = "ssl_cert_c_num";
    /**
     * ssl_cert_s_hash字段
     */
    public static final String FIELD_SSL_CERT_S_HASH = "ssl_cert_s_hash";
    /**
     * ssl_cert_s_num字段
     */
    public static final String FIELD_SSL_CERT_S_NUM = "ssl_cert_s_num";
    /**
     * ssl_hello_c_alpn字段
     */
    public static final String FIELD_SSL_HELLO_C_ALPN = "ssl_hello_c_alpn";
    /**
     * ssl_hello_c_ciphersuit字段
     */
    public static final String FIELD_SSL_HELLO_C_CIPHERSUIT = "ssl_hello_c_ciphersuit";
    /**
     * ssl_hello_c_ciphersuitnum字段
     */
    public static final String FIELD_SSL_HELLO_C_CIPHERSUITNUM = "ssl_hello_c_ciphersuitnum";
    /**
     * ssl_hello_c_compressionmethod字段
     */
    public static final String FIELD_SSL_HELLO_C_COMPRESSIONMETHOD = "ssl_hello_c_compressionmethod";
    /**
     * ssl_hello_c_compressionmethodlen字段
     */
    public static final String FIELD_SSL_HELLO_C_COMPRESSIONMETHODLEN = "ssl_hello_c_compressionmethodlen";
    /**
     * ssl_hello_c_extention字段
     */
    public static final String FIELD_SSL_HELLO_C_EXTENTION = "ssl_hello_c_extention";
    /**
     * ssl_hello_c_extentionnum字段
     */
    public static final String FIELD_SSL_HELLO_C_EXTENTIONNUM = "ssl_hello_c_extentionnum";
    /**
     * ssl_hello_c_random字段
     */
    public static final String FIELD_SSL_HELLO_C_RANDOM = "ssl_hello_c_random";
    /**
     * ssl_hello_c_servername字段
     */
    public static final String FIELD_SSL_HELLO_C_SERVERNAME = "ssl_hello_c_servername";
    /**
     * ssl_hello_c_servernametype字段
     */
    public static final String FIELD_SSL_HELLO_C_SERVERNAMETYPE = "ssl_hello_c_servernametype";
    /**
     * ssl_hello_c_sessionid字段
     */
    public static final String FIELD_SSL_HELLO_C_SESSIONID = "ssl_hello_c_sessionid";
    /**
     * ssl_hello_c_sessionidlen字段
     */
    public static final String FIELD_SSL_HELLO_C_SESSIONIDLEN = "ssl_hello_c_sessionidlen";
    /**
     * ssl_hello_c_sessionticket字段
     */
    public static final String FIELD_SSL_HELLO_C_SESSIONTICKET = "ssl_hello_c_sessionticket";
    /**
     * ssl_hello_c_time字段
     */
    public static final String FIELD_SSL_HELLO_C_TIME = "ssl_hello_c_time";
    /**
     * ssl_hello_c_version字段
     */
    public static final String FIELD_SSL_HELLO_C_VERSION = "ssl_hello_c_version";
    /**
     * ssl_hello_s_alpn字段
     */
    public static final String FIELD_SSL_HELLO_S_ALPN = "ssl_hello_s_alpn";
    /**
     * ssl_hello_s_cipersuite字段
     */
    public static final String FIELD_SSL_HELLO_S_CIPERSUITE = "ssl_hello_s_cipersuite";
    /**
     * ssl_hello_s_compressionmethod字段
     */
    public static final String FIELD_SSL_HELLO_S_COMPRESSIONMETHOD = "ssl_hello_s_compressionmethod";
    /**
     * ssl_hello_s_extention字段
     */
    public static final String FIELD_SSL_HELLO_S_EXTENTION = "ssl_hello_s_extention";
    /**
     * ssl_hello_s_extentionnum字段
     */
    public static final String FIELD_SSL_HELLO_S_EXTENTIONNUM = "ssl_hello_s_extentionnum";
    /**
     * ssl_hello_s_random字段
     */
    public static final String FIELD_SSL_HELLO_S_RANDOM = "ssl_hello_s_random";
    /**
     * ssl_hello_s_sessionid字段
     */
    public static final String FIELD_SSL_HELLO_S_SESSIONID = "ssl_hello_s_sessionid";
    /**
     * ssl_hello_s_sessionidlen字段
     */
    public static final String FIELD_SSL_HELLO_S_SESSIONIDLEN = "ssl_hello_s_sessionidlen";
    /**
     * ssl_hello_s_sessionticket字段
     */
    public static final String FIELD_SSL_HELLO_S_SESSIONTICKET = "ssl_hello_s_sessionticket";
    /**
     * ssl_hello_s_time字段
     */
    public static final String FIELD_SSL_HELLO_S_TIME = "ssl_hello_s_time";
    /**
     * ssl_hello_s_version字段
     */
    public static final String FIELD_SSL_HELLO_S_VERSION = "ssl_hello_s_version";
    /**
     * ssl_s_keyexchange字段
     */
    public static final String FIELD_SSL_S_KEYEXCHANGE = "ssl_s_keyexchange";
    /**
     * ssl_s_keyexchangelen字段
     */
    public static final String FIELD_SSL_S_KEYEXCHANGELEN = "ssl_s_keyexchangelen";
    /**
     * ssl_s_newsessionticket_lifetime字段
     */
    public static final String FIELD_SSL_S_NEWSESSIONTICKET_LIFETIME = "ssl_s_newsessionticket_lifetime";
    /**
     * ssl_s_newsessionticket_ticket字段
     */
    public static final String FIELD_SSL_S_NEWSESSIONTICKET_TICKET = "ssl_s_newsessionticket_ticket";
    /**
     * ssl_s_newsessionticket_ticketlen字段
     */
    public static final String FIELD_SSL_S_NEWSESSIONTICKET_TICKETLEN = "ssl_s_newsessionticket_ticketlen";
    /**
     * ssl_version字段
     */
    public static final String FIELD_SSL_VERSION = "ssl_version";

    /** TERMINAL 相关字段 */
    /**
     * columns字段
     */
    public static final String FIELD_COLUMNS = "columns";
    /**
     * magic_cookie字段
     */
    public static final String FIELD_MAGIC_COOKIE = "magic_cookie";
    /**
     * rows字段
     */
    public static final String FIELD_ROWS = "rows";
    /**
     * term_speed字段
     */
    public static final String FIELD_TERM_SPEED = "term_speed";
    /**
     * term_type字段
     */
    public static final String FIELD_TERM_TYPE = "term_type";
    /**
     * winsize_marker字段
     */
    public static final String FIELD_WINSIZE_MARKER = "winsize_marker";
    /**
     * x_pixels字段
     */
    public static final String FIELD_X_PIXELS = "x_pixels";
    /**
     * y_pixels字段
     */
    public static final String FIELD_Y_PIXELS = "y_pixels";

    /** XDMCP 相关字段 */
    /**
     * xdmcp_session_id字段
     */
    public static final String FIELD_XDMCP_SESSION_ID = "xdmcp_session_id";
    /**
     * tcp_s_feature_tcpopt_layout字段
     */
    public static final String FIELD_TCP_S_FEATURE_TCPOPT_LAYOUT = "tcp_s_feature_tcpopt_layout";

    /** 新增的常量 */
    /**
     * authentication_result字段
     */
    public static final String FIELD_AUTHENTICATION_RESULT = "authentication_result";
    /**
     * big_endian_flag字段
     */
    public static final String FIELD_BIG_ENDIAN_FLAG = "big_endian_flag";
    /**
     * bits_per_pixel字段
     */
    public static final String FIELD_BITS_PER_PIXEL = "bits_per_pixel";
    /**
     * blue_maximum字段
     */
    public static final String FIELD_BLUE_MAXIMUM = "blue_maximum";
    /**
     * blue_shift字段
     */
    public static final String FIELD_BLUE_SHIFT = "blue_shift";
    /**
     * channel_count字段
     */
    public static final String FIELD_CHANNEL_COUNT = "channel_count";
    /**
     * check_sum字段
     */
    public static final String FIELD_CHECK_SUM = "check_sum";
    /**
     * check_sum_reply字段
     */
    public static final String FIELD_CHECK_SUM_REPLY = "check_sum_reply";
    public static final String FIELD_CLI_JA3 = "cli_ja3";
    /**
     * color_depth字段
     */
    public static final String FIELD_COLOR_DEPTH = "color_depth";
    /**
     * connection_addresses字段
     */
    public static final String FIELD_CONNECTION_ADDRESSES = "connection_addresses";
    /**
     * connection_type字段
     */
    public static final String FIELD_CONNECTION_TYPE = "connection_type";
    /**
     * connection_types字段
     */
    public static final String FIELD_CONNECTION_TYPES = "connection_types";
    /**
     * cookie字段
     */
    public static final String FIELD_COOKIE = "cookie";
    /**
     * c_flag_protocols字段
     */
    public static final String FIELD_C_FLAG_PROTOCOLS = "c_flag_protocols";
    /**
     * c_requested_protocols字段
     */
    public static final String FIELD_C_REQUESTED_PROTOCOLS = "c_requested_protocols";
    /**
     * depth字段
     */
    public static final String FIELD_DEPTH = "depth";
    /**
     * dh_e字段
     */
    public static final String FIELD_DH_E = "dh_e";
    /**
     * dh_f字段
     */
    public static final String FIELD_DH_F = "dh_f";
    /**
     * dh_gex_g字段
     */
    public static final String FIELD_DH_GEX_G = "dh_gex_g";
    /**
     * dh_gex_max字段
     */
    public static final String FIELD_DH_GEX_MAX = "dh_gex_max";
    /**
     * dh_gex_min字段
     */
    public static final String FIELD_DH_GEX_MIN = "dh_gex_min";
    /**
     * dh_gex_nbits字段
     */
    public static final String FIELD_DH_GEX_NBITS = "dh_gex_nbits";
    /**
     * dh_gex_p字段
     */
    public static final String FIELD_DH_GEX_P = "dh_gex_p";
    /**
     * display_class字段
     */
    public static final String FIELD_DISPLAY_CLASS = "display_class";
    /**
     * display_number字段
     */
    public static final String FIELD_DISPLAY_NUMBER = "display_number";
    /**
     * ecdh_q_c字段
     */
    public static final String FIELD_ECDH_Q_C = "ecdh_q_c";
    /**
     * ecdh_q_s字段
     */
    public static final String FIELD_ECDH_Q_S = "ecdh_q_s";
    /**
     * encoding_types字段
     */
    public static final String FIELD_ENCODING_TYPES = "encoding_types";
    /**
     * exc_dst_addr字段
     */
    public static final String FIELD_EXC_DST_ADDR = "exc_dst_addr";
    /**
     * exc_dst_port字段
     */
    public static final String FIELD_EXC_DST_PORT = "exc_dst_port";
    /**
     * exc_pointer字段
     */
    public static final String FIELD_EXC_POINTER = "exc_pointer";
    /**
     * exc_prot字段
     */
    public static final String FIELD_EXC_PROT = "exc_prot";
    /**
     * exc_src_addr字段
     */
    public static final String FIELD_EXC_SRC_ADDR = "exc_src_addr";
    /**
     * exc_src_port字段
     */
    public static final String FIELD_EXC_SRC_PORT = "exc_src_port";
    /**
     * exc_ttl字段
     */
    public static final String FIELD_EXC_TTL = "exc_ttl";
    /**
     * fb_height字段
     */
    public static final String FIELD_FB_HEIGHT = "fb_height";
    /**
     * fb_width字段
     */
    public static final String FIELD_FB_WIDTH = "fb_width";
    /**
     * green_maximum字段
     */
    public static final String FIELD_GREEN_MAXIMUM = "green_maximum";
    /**
     * green_shift字段
     */
    public static final String FIELD_GREEN_SHIFT = "green_shift";
    /**
     * gw_addr字段
     */
    public static final String FIELD_GW_ADDR = "gw_addr";
    /**
     * hassh字段
     */
    public static final String FIELD_HASSH = "hassh";
    /**
     * hostname字段
     */
    public static final String FIELD_HOSTNAME = "hostname";
    /**
     * host_key_dsa_g字段
     */
    public static final String FIELD_HOST_KEY_DSA_G = "host_key_dsa_g";
    /**
     * host_key_dsa_p字段
     */
    public static final String FIELD_HOST_KEY_DSA_P = "host_key_dsa_p";
    /**
     * host_key_dsa_q字段
     */
    public static final String FIELD_HOST_KEY_DSA_Q = "host_key_dsa_q";
    /**
     * host_key_dsa_y字段
     */
    public static final String FIELD_HOST_KEY_DSA_Y = "host_key_dsa_y";
    /**
     * host_key_ecdsa_id字段
     */
    public static final String FIELD_HOST_KEY_ECDSA_ID = "host_key_ecdsa_id";
    /**
     * host_key_ecdsa_q字段
     */
    public static final String FIELD_HOST_KEY_ECDSA_Q = "host_key_ecdsa_q";
    /**
     * host_key_eddsa_key字段
     */
    public static final String FIELD_HOST_KEY_EDDSA_KEY = "host_key_eddsa_key";
    /**
     * host_key_rsa_e字段
     */
    public static final String FIELD_HOST_KEY_RSA_E = "host_key_rsa_e";
    /**
     * host_key_rsa_n字段
     */
    public static final String FIELD_HOST_KEY_RSA_N = "host_key_rsa_n";
    /**
     * host_key_type字段
     */
    public static final String FIELD_HOST_KEY_TYPE = "host_key_type";
    /**
     * ime_filename字段
     */
    public static final String FIELD_IME_FILENAME = "ime_filename";
    /**
     * joy字段
     */
    public static final String FIELD_JOY = "joy";
    /**
     * joys字段
     */
    public static final String FIELD_JOYS = "joys";
    /**
     * joy_fp字段
     */
    public static final String FIELD_JOY_FP = "joy_fp";
    /**
     * kex_h_sig字段
     */
    public static final String FIELD_KEX_H_SIG = "kex_h_sig";
    /**
     * kex_h_sig_type字段
     */
    public static final String FIELD_KEX_H_SIG_TYPE = "kex_h_sig_type";
    /**
     * manufacture_disp_id字段
     */
    public static final String FIELD_MANUFACTURE_DISP_ID = "manufacture_disp_id";
    /**
     * mask字段
     */
    public static final String FIELD_MASK = "mask";
    /**
     * mcs_channelid字段
     */
    public static final String FIELD_MCS_CHANNELID = "mcs_channelid";
    /**
     * mul_cast_addr字段
     */
    public static final String FIELD_MUL_CAST_ADDR = "mul_cast_addr";
    /**
     * ndp_cur_mtu字段
     */
    public static final String FIELD_NDP_CUR_MTU = "ndp_cur_mtu";
    /**
     * ndp_life_time字段
     */
    public static final String FIELD_NDP_LIFE_TIME = "ndp_life_time";
    /**
     * ndp_link_addr字段
     */
    public static final String FIELD_NDP_LINK_ADDR = "ndp_link_addr";
    /**
     * ndp_pre_fix字段
     */
    public static final String FIELD_NDP_PRE_FIX = "ndp_pre_fix";
    /**
     * ndp_pre_len字段
     */
    public static final String FIELD_NDP_PRE_LEN = "ndp_pre_len";
    /**
     * ndp_tar_addr字段
     */
    public static final String FIELD_NDP_TAR_ADDR = "ndp_tar_addr";
    /**
     * ndp_val_life_time字段
     */
    public static final String FIELD_NDP_VAL_LIFE_TIME = "ndp_val_life_time";
    /**
     * next_hop_mtu字段
     */
    public static final String FIELD_NEXT_HOP_MTU = "next_hop_mtu";
    /**
     * orig_time_stamp字段
     */
    public static final String FIELD_ORIG_TIME_STAMP = "orig_time_stamp";
    /**
     * qur_dns字段
     */
    public static final String FIELD_QUR_DNS = "qur_dns";
    public static final String FIELD_QUR_IPV4_ADDR = "qur_ipv4_addr";
    public static final String FIELD_QUR_IPV6_ADDR = "qur_ipv6_addr";
    /**
     * qur_type字段
     */
    public static final String FIELD_QUR_TYPE = "qur_type";
    /**
     * recv_time_stamp字段
     */
    public static final String FIELD_RECV_TIME_STAMP = "recv_time_stamp";
    /**
     * red_maximum字段
     */
    public static final String FIELD_RED_MAXIMUM = "red_maximum";
    /**
     * red_shift字段
     */
    public static final String FIELD_RED_SHIFT = "red_shift";
    /**
     * rep_ttl字段
     */
    public static final String FIELD_REP_TTL = "rep_ttl";
    /**
     * response_time字段
     */
    public static final String FIELD_RESPONSE_TIME = "response_time";
    /**
     * res_time字段
     */
    public static final String FIELD_RES_TIME = "res_time";
    /**
     * rtraddr字段
     */
    public static final String FIELD_RTRADDR = "rtraddr";
    /**
     * rtr_time_out字段
     */
    public static final String FIELD_RTR_TIME_OUT = "rtr_time_out";
    /**
     * sas_sequence字段
     */
    public static final String FIELD_SAS_SEQUENCE = "sas_sequence";
    /**
     * selected_protocols字段
     */
    public static final String FIELD_SELECTED_PROTOCOLS = "selected_protocols";
    /**
     * share_dsktp_flag字段
     */
    public static final String FIELD_SHARE_DSKTP_FLAG = "share_dsktp_flag";
    public static final String FIELD_SRVHOSTKEYFP256 = "srvhostkeyfp256";
    /**
     * srv_hassh字段
     */
    public static final String FIELD_SRV_HASSH = "srv_hassh";
    public static final String FIELD_SRV_JA3 = "srv_ja3";
    /**
     * status字段
     */
    public static final String FIELD_STATUS = "status";
    /**
     * sub_net_id字段
     */
    public static final String FIELD_SUB_NET_ID = "sub_net_id";
    /**
     * s_flag_protocols字段
     */
    public static final String FIELD_S_FLAG_PROTOCOLS = "s_flag_protocols";
    /**
     * s_selected_protocols字段
     */
    public static final String FIELD_S_SELECTED_PROTOCOLS = "s_selected_protocols";
    /**
     * term_height字段
     */
    public static final String FIELD_TERM_HEIGHT = "term_height";
    /**
     * term_width字段
     */
    public static final String FIELD_TERM_WIDTH = "term_width";
    /**
     * trans_time_stamp字段
     */
    public static final String FIELD_TRANS_TIME_STAMP = "trans_time_stamp";
    /**
     * true_color_flag字段
     */
    public static final String FIELD_TRUE_COLOR_FLAG = "true_color_flag";
    /**
     * ttl字段
     */
    public static final String FIELD_TTL = "ttl";
    /**
     * unc_ttl字段
     */
    public static final String FIELD_UNC_TTL = "unc_ttl";
    /**
     * unreachable_destination_port字段
     */
    public static final String FIELD_UNREACHABLE_DESTINATION_PORT = "unreachable_destination_port";
    /**
     * unreachable_source_port字段
     */
    public static final String FIELD_UNREACHABLE_SOURCE_PORT = "unreachable_source_port";
    /**
     * unr_dst_addr字段
     */
    public static final String FIELD_UNR_DST_ADDR = "unr_dst_addr";
    /**
     * unr_prot字段
     */
    public static final String FIELD_UNR_PROT = "unr_prot";
    /**
     * unr_src_addr字段
     */
    public static final String FIELD_UNR_SRC_ADDR = "unr_src_addr";
    /**
     * username字段
     */
    public static final String FIELD_USERNAME = "username";
    /**
     * version字段
     */
    public static final String FIELD_VERSION = "version";
    /** S7 相关字段 */
    /**
     * tpkt_version
     */
    public static final String TPKT_VERSION = "tpkt_version";
    /**
     * cotp_type
     */
    public static final String COTP_TYPE = "cotp_type";
    /**
     * s7_type
     */
    public static final String S7_TYPE = "s7_type";
    /**
     * s7_function
     */
    public static final String S7_FUNCTION = "s7_function";
    /**
     * system_type
     */
    public static final String SYSTEM_TYPE = "system_type";
    /**
     * system_group_function
     */
    public static final String SYSTEM_GROUP_FUNCTION = "system_group_function";
    /**
     * system_sub_function
     */
    public static final String SYSTEM_SUB_FUNCTION = "system_sub_function";
    /**
     * dst_ref
     */
    public static final String DST_REF = "dst_ref";
    /**
     * src_ref
     */
    public static final String SRC_REF = "src_ref";
    /**
     * pdu_size
     */
    public static final String PDU_SIZE = "pdu_size";
    /**
     * src_connect_type
     */
    public static final String SRC_CONNECT_TYPE = "src_connect_type";
    /**
     * src_rack
     */
    public static final String SRC_RACK = "src_rack";
    /**
     * src_slot
     */
    public static final String SRC_SLOT = "src_slot";
    /**
     * dst_connect_type
     */
    public static final String DST_CONNECT_TYPE = "dst_connect_type";
    /**
     * dst_rack
     */
    public static final String DST_RACK = "dst_rack";
    /**
     * dst_slot
     */
    public static final String DST_SLOT = "dst_slot";
    /**
     * packet_c2s
     */
    public static final String PACKET_C2S = "packet_c2s";
    /**
     * MODBUS  EIP IEC104 OPC 相关字段
     */
        /** trans_id */
    public static final String TRANS_ID = "trans_id";
        /** protocol_id */
    public static final String PROTOCOL_ID = "protocol_id";
        /** slave_id */
    public static final String SLAVE_ID = "slave_id";
        /** func_code */
    public static final String FUNC_CODE = "func_code";

    /** ESP 相关字段*/
    /**
     * protocol_family
     */
    public static final String PROTOCOL_FAMILY = "protocol_family";
    /**
     * communication_rate
     */
    public static final String COMMUNICATION_RATE = "communication_rate";
    /**
     * direction
     */
    public static final String DIRECTION = "direction";
    /**
     * encapsulation_mode
     */
    public static final String ENCAPSULATION_MODE = "encapsulation_mode";
    /**
     * esp_spi
     */
    public static final String ESP_SPI = "esp_spi";
    /**
     * esp_seq
     */
    public static final String ESP_SEQ = "esp_seq";
    /**
     * esp_data_len
     */
    public static final String ESP_DATA_LEN = "esp_data_len";
    /**
     * sp_data
     */
    public static final String SP_DATA = "sp_data";
    /** L2TP */
        /** protocol_version */
    public static final String PROTOCOL_VERSION = "protocol_version";
        /** framing_capabilities */
    public static final String FRAMING_CAPABILITIES = "framing_capabilities";
        /** bearer_capabilities */
    public static final String BEARER_CAPABILITIES = "bearer_capabilities";
        /** server_hostname */
    public static final String SERVER_HOSTNAME = "server_hostname";
        /** client_hostname */
    public static final String CLIENT_HOSTNAME = "client_hostname";
        /** server_vendorname */
    public static final String SERVER_VENDORNAME = "server_vendorname";
        /** client_vendorname */
    public static final String CLIENT_VENDORNAME = "client_vendorname";
        /** calling_number */
    public static final String CALLING_NUMBER = "calling_number";
        /** proxy_authen_type */
    public static final String PROXY_AUTHEN_TYPE = "proxy_authen_type";
        /** proxy_authen_name */
    public static final String PROXY_AUTHEN_NAME = "proxy_authen_name";
        /** is_negotiate_success */
    public static final String IS_NEGOTIATE_SUCCESS = "is_negotiate_success";
    /** 会话协议元数据字段 */
    public static final String SESSION_HTTP_LIST = "http";
    public static final String SESSION_DNS_LIST = "dns";
    public static final String SESSION_SSL_LIST = "ssl";
    /** 维度表时间字段 */
        /** 创建时间 */
    public static final String KEY_DW_CREATION_TIMESTAMP = "dw_creation_timestamp";
        /** 更新时间 */
    public static final String KEY_DW_LAST_UPDATED_TIMESTAMP = "dw_last_updated_timestamp";

    /** 图TAG相关字段 */
    /** app */
    public static final String KEY_APP_NAME = "app_name";
    public static final String KEY_APP_VERSION = "app_version";
    /** app service */
    public static final String KEY_APP_DIP = "ip";
    public static final String KEY_APP_DPORT = "dport";
    public static final String KEY_APP_IPPRO = "ip_protocol";
    public static final String KEY_THREAT_SCORE = "threat_score";
    public static final String KEY_TRUST_SCORE = "trust_score";
    /** fingerprint */
    public static final String KEY_JA3_HASH = "ja3_hash";
    public static final String KEY_FINGERPRINT_TYPE = "type";
    public static final String KEY_FINGERPRINT_DESC = "finger_desc";

    /**
     * 防止实例化
     */
    private FieldConstants() {
        throw new IllegalStateException("Utility class");
    }
}
