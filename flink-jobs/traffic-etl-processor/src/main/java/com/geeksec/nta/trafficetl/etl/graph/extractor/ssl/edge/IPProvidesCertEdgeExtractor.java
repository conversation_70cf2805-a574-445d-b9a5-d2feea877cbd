package com.geeksec.nta.trafficetl.etl.graph.extractor.ssl.edge;

import com.geeksec.common.infrastructure.network.DomainUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.common.utils.net.DomainUtils;
import com.geeksec.nta.trafficetl.etl.constant.FieldConstants;
import com.geeksec.nta.trafficetl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.trafficetl.sink.tag.NebulaGraphOutputTag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Description null.java
 * @Date 17:11$ 2025/6/17$
 **/
@Slf4j
public class IPProvidesCertEdgeExtractor extends BaseEdgeExtractor {
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.IP_PROVIDES_CERT_TAG;
    }

    /**
     * IP提供证书 (源IP -> CERT) client -> 客户端证书， server -> 服务端证书
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        List<Row> edges = new ArrayList<>();
        try {
            String sIP = value.getField(FieldConstants.FIELD_SRC_IP).toString();
            String dIP = value.getField(FieldConstants.FIELD_DST_IP).toString();
            String sni = value.getField(FieldConstants.FIELD_SSL_HELLO_C_SERVERNAME).toString();
            String sCertHash = value.getField(FieldConstants.FIELD_SSL_CERT_C_HASH).toString();
            String dCertHash = value.getField(FieldConstants.FIELD_SSL_CERT_S_HASH).toString();
            List<String> sCertHashes = objectMapper.readValue(sCertHash, new TypeReference<List<String>>() {});
            List<String> dCertHashes = objectMapper.readValue(dCertHash, new TypeReference<List<String>>() {});

            if (DomainUtils.isValidDomain(sni) && !sni.equals(dIP)) {
                sni = DomainUtils.formatDomain(sni);
            } else {
                sni = StringUtils.EMPTY;
            }

            if (CollectionUtils.isNotEmpty(dCertHashes)){
                edges.add(Row.of(sIP, sCertHashes.get(0).toString(),
                        0, // rank暂定0
                        "client", sni));
            }

            if (CollectionUtils.isNotEmpty(sCertHashes)){
                edges.add(Row.of(sIP, dCertHashes.get(0).toString(),
                        0, // rank暂定0
                        "server", sni));
            }
        } catch (JsonProcessingException e) {
            log.error("Parse SSL CertHash Error {}", e.getMessage());
        }

        return edges;
    }
}
