package com.geeksec.nta.datawarehouse.etl.ods.converter.protocol;

import com.geeksec.common.toolkit.time.TimeUtils;
import com.geeksec.nta.datawarehouse.etl.constant.FieldConstants;
import com.geeksec.nta.datawarehouse.etl.ods.converter.common.AbstractProtobufMessageConverter;
import com.geeksec.nta.datawarehouse.etl.ods.tag.MessageOutputTag;
import com.geeksec.proto.ZMPNMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * ESP协议转换器
 * 将ESP协议的protobuf消息转换为Doris ods_esp_protocol_metadata表格式
 *
 * <AUTHOR>
 */
@Slf4j
public class EspConverter extends AbstractProtobufMessageConverter {

    @Override
    protected Row convertMessage(ZMPNMsg.JKNmsg msg) {
        if (!msg.hasEsp()) {
            log.warn("JKNmsg does not contain Esp message");
            return null;
        }
        Row row = Row.withNames();
        ZMPNMsg.esp_msg espMsg = msg.getEsp();
        if (espMsg.hasCommMsg()) {
            // 设置通用字段
            enrichComMsg(row, espMsg.getCommMsg());
        }

        // 设置ESP特定字段（需要根据实际protobuf定义实现）
        // TODO: 根据实际的ESP protobuf消息结构实现字段映射
        row.setField(FieldConstants.PROTOCOL_FAMILY, espMsg.getProtocolFamily());
        row.setField(FieldConstants.COMMUNICATION_RATE, espMsg.getCommunicationRate());
        row.setField(FieldConstants.DIRECTION, espMsg.getDirection());
        row.setField(FieldConstants.ENCAPSULATION_MODE, espMsg.getEncapsulationMode());
        row.setField(FieldConstants.ESP_SPI, espMsg.getEspSpi());
        row.setField(FieldConstants.ESP_SEQ, espMsg.getEspSeq());
        row.setField(FieldConstants.ESP_DATA_LEN, espMsg.getEspDataLen());
        row.setField(FieldConstants.SP_DATA, espMsg.getEspData());
        return row;
    }

    @Override
    public OutputTag<Row> getOutputTag() {
        return MessageOutputTag.ESP_STREAM;
    }
}
