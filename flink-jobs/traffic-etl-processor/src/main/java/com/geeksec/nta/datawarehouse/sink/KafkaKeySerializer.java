package com.geeksec.nta.datawarehouse.sink;

import com.geeksec.nta.datawarehouse.etl.constant.FieldConstants;
import org.apache.flink.api.common.serialization.SerializationSchema;
import org.apache.flink.types.Row;
import org.apache.flink.util.Preconditions;

import java.nio.charset.StandardCharsets;

/**
 * Kafka消息键序列化器，将会话ID作为Kafka消息的键
 *
 * 用于向graph-builder发送会话和协议元数据时设置消息键
 * 使用会话ID作为键可以确保相同会话的消息被发送到相同的分区，
 * 从而在graph-builder中保持会话数据的顺序性和一致性
 *
 * <AUTHOR>
 */
public class KafkaKeySerializer implements SerializationSchema<Row> {

    private static final long serialVersionUID = 1L;

    @Override
    public byte[] serialize(Row row) {
        Preconditions.checkNotNull(row, "Row cannot be null");
        Object sessionId = row.getField(FieldConstants.FIELD_SESSION_ID);
        Preconditions.checkNotNull(sessionId, "session_id field cannot be null in row: " + row);

        String sessionIdStr = sessionId.toString();
        Preconditions.checkNotNull(sessionIdStr, "session_id string representation cannot be null");

        return sessionIdStr.getBytes(StandardCharsets.UTF_8);
    }
}
