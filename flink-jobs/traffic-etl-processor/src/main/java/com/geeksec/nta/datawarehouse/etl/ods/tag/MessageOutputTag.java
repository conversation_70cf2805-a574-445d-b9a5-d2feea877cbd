package com.geeksec.nta.datawarehouse.etl.ods.tag;

import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * 消息输出标签
 * 用于在Flink流处理中标记不同协议数据的侧输出流
 *
 * <AUTHOR>
 */
public final class MessageOutputTag{

    /**
     * 私有构造函数，防止实例化
     */
    private MessageOutputTag() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 会话流输出标签
     * 用于输出会话连接信息
     */
    public static final OutputTag<Row> SESSION_STREAM =
            new OutputTag<Row>("SESSION") {};

    /**
     * HTTP流输出标签
     * 用于输出HTTP协议元数据
     */
    public static final OutputTag<Row> HTTP_STREAM =
            new OutputTag<Row>("HTTP") {};

    /**
     * DNS流输出标签
     * 用于输出DNS协议元数据
     */
    public static final OutputTag<Row> DNS_STREAM =
            new OutputTag<Row>("DNS") {};

    /**
     * SSL流输出标签
     * 用于输出SSL协议元数据
     */
    public static final OutputTag<Row> SSL_STREAM =
            new OutputTag<Row>("SSL") {};

    /**
     * SSH流输出标签
     * 用于输出SSH协议元数据
     */
    public static final OutputTag<Row> SSH_STREAM =
            new OutputTag<Row>("SSH") {};

    /**
     * RLOGIN流输出标签
     * 用于输出RLOGIN协议元数据
     */
    public static final OutputTag<Row> RLOGIN_STREAM =
            new OutputTag<Row>("RLOGIN") {};

    /**
     * TELNET流输出标签
     * 用于输出TELNET协议元数据
     */
    public static final OutputTag<Row> TELNET_STREAM =
            new OutputTag<Row>("TELNET") {};

    /**
     * RDP流输出标签
     * 用于输出RDP协议元数据
     */
    public static final OutputTag<Row> RDP_STREAM =
            new OutputTag<Row>("RDP") {};

    /**
     * VNC流输出标签
     * 用于输出VNC协议元数据
     */
    public static final OutputTag<Row> VNC_STREAM =
            new OutputTag<Row>("VNC") {};

    /**
     * XDMCP流输出标签
     * 用于输出XDMCP协议元数据
     */
    public static final OutputTag<Row> XDMCP_STREAM =
            new OutputTag<Row>("XDMCP") {};

    /**
     * NTP流输出标签
     * 用于输出NTP协议元数据
     */
    public static final OutputTag<Row> NTP_STREAM =
            new OutputTag<Row>("NTP") {};

    /**
     * ICMP流输出标签
     * 用于输出ICMP协议元数据
     */
    public static final OutputTag<Row> ICMP_STREAM =
            new OutputTag<Row>("ICMP") {};

    /**
     * S7流输出标签
     * 用于输出S7协议元数据
     */
    public static final OutputTag<Row> S7_STREAM =
            new OutputTag<Row>("S7") {};

    /**
     * Modbus流输出标签
     * 用于输出Modbus协议元数据
     */
    public static final OutputTag<Row> MODBUS_STREAM =
            new OutputTag<Row>("MODBUS") {};

    /**
     * IEC104流输出标签
     * 用于输出IEC104协议元数据
     */
    public static final OutputTag<Row> IEC104_STREAM =
            new OutputTag<Row>("IEC104") {};

    /**
     * EIP流输出标签
     * 用于输出EIP协议元数据
     */
    public static final OutputTag<Row> EIP_STREAM =
            new OutputTag<Row>("EIP") {};

    /**
     * OPC流输出标签
     * 用于输出OPC协议元数据
     */
    public static final OutputTag<Row> OPC_STREAM =
            new OutputTag<Row>("OPC") {};

    /**
     * ESP流输出标签
     * 用于输出ESP协议元数据
     */
    public static final OutputTag<Row> ESP_STREAM =
            new OutputTag<Row>("ESP") {};

    /**
     * L2TP流输出标签
     * 用于输出L2TP协议元数据
     */
    public static final OutputTag<Row> L2TP_STREAM =
            new OutputTag<Row>("L2TP") {};

}
