package com.geeksec.nta.datawarehouse.etl.graph.extractor.session.edge;

import com.geeksec.nta.datawarehouse.etl.constant.FieldConstants;
import com.geeksec.nta.datawarehouse.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.datawarehouse.sink.tag.NebulaGraphOutputTag;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.util.List;

/**
 * @Description null.java
 * @Date 17:11$ 2025/6/17$
 **/
public class IPMapsMacEdgeExtractor extends BaseEdgeExtractor {
    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.IP_MAPS_TO_MAC_TAG;
    }

    /**
     * 目标地址映射 (目的IP -> 目的MAC)
     * 源地址映射关系 (源IP -> 源MAC)
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        // 获取目的IP和目的MAC
        String dIP = value.getField(FieldConstants.FIELD_DST_IP).toString();
        String dMac = value.getField(FieldConstants.FIELD_DMAC).toString();

        // 获取源IP和源MAC
        String sIP = value.getField(FieldConstants.FIELD_SRC_IP).toString();
        String sMac = value.getField(FieldConstants.FIELD_SMAC).toString();

        return List.of(
                Row.of(dIP, dMac,
                        0 // rank暂定0
                ),
                Row.of(sIP, sMac,
                        0 // rank暂定0
                ));
    }
}
