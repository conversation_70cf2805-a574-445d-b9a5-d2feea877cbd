package com.geeksec.nta.trafficetl.etl.graph.extractor.session.edge;

import com.geeksec.nta.trafficetl.etl.constant.FieldConstants;
import com.geeksec.nta.trafficetl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.trafficetl.sink.tag.NebulaGraphOutputTag;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.util.List;

/**
 * @Description null.java
 * @Date 9:30$ 2025/6/18$
 **/
public class ClientAccessAppEdgeExtractor extends BaseEdgeExtractor {
    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.CLIENT_ACCESSES_APP_TAG;
    }

    /**
     * 客户端访问应用服务 (IP -> APPSERVICE)
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        // 获取源IP
        String sIp = value.getField(FieldConstants.FIELD_SRC_IP).toString();
        // 获取应用信息
        String dIp = value.getField(FieldConstants.FIELD_DST_IP).toString();
        Integer dPort = Integer.valueOf(value.getField(FieldConstants.FIELD_DST_PORT).toString());
        String appName = value.getField(FieldConstants.FIELD_APP_NAME).toString();
        String appKey = dIp + "_" + dPort + "_" + appName;
        return List.of(Row.of(sIp, appKey,
                    0 // rank 暂定0
                ));
    }
}
