package com.geeksec.nta.datawarehouse.etl.graph.extractor.base;

import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixList;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixListFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.util.Map;

/**
 * 基础顶点提取器
 * 所有顶点提取器的基类，提供公共方法和初始化逻辑
 *
 * <AUTHOR> Team
 */
@Slf4j
public abstract class BaseVertexExtractor extends RichFlatMapFunction<Map<String, Object>, Map<String, Object>> {

    /** 公共后缀列表工厂 */
    protected static PublicSuffixListFactory factory = null;

    /** 公共后缀列表 */
    protected static PublicSuffixList suffixList = null;
    // ==================== 顶点类型定义 ====================
    /** IP地址顶点 */
    private static final OutputTag<Row> IP_TAG = new OutputTag<Row>("IP") {};

    /** 攻击者顶点 */
    private static final OutputTag<Row> ATTACKER_TAG = new OutputTag<Row>("ATTACKER") {};

    /** 受害者顶点 */
    private static final OutputTag<Row> VICTIM_TAG = new OutputTag<Row>("VICTIM") {};

    /** MAC地址顶点 */
    private static final OutputTag<Row> MAC_TAG = new OutputTag<Row>("MAC") {};

    /** 应用服务顶点 */
    private static final OutputTag<Row> APPSERVICE_TAG = new OutputTag<Row>("APPSERVICE") {};

    /** 域名顶点 */
    private static final OutputTag<Row> DOMAIN_TAG = new OutputTag<Row>("DOMAIN") {};

    /** 锚域名顶点 */
    private static final OutputTag<Row> FDOMAIN_TAG = new OutputTag<Row>("FDOMAIN") {};

    /** TLS证书顶点 */
    private static final OutputTag<Row> CERT_TAG = new OutputTag<Row>("CERT") {};

    /** 组织机构顶点 */
    private static final OutputTag<Row> ORG_TAG = new OutputTag<Row>("ORG") {};

    /** TLS指纹顶点 */
    private static final OutputTag<Row> SSLFINGER_TAG = new OutputTag<Row>("SSLFINGER") {};

    /** User-Agent顶点 */
    private static final OutputTag<Row> UA_TAG = new OutputTag<Row>("UA") {};

    /** 设备类型顶点 */
    private static final OutputTag<Row> DEVICE_TAG = new OutputTag<Row>("DEVICE") {};

    /** 操作系统顶点 */
    private static final OutputTag<Row> OS_TAG = new OutputTag<Row>("OS") {};

    /** 应用顶点 */
    private static final OutputTag<Row> APP_TAG = new OutputTag<Row>("APP") {};

    /** 标签顶点 */
    private static final OutputTag<Row> LABEL_TAG = new OutputTag<Row>("LABEL") {};

    /** URL地址顶点 */
    private static final OutputTag<Row> URL_TAG = new OutputTag<Row>("URL") {};

    /** 证书颁发机构顶点 */
    private static final OutputTag<Row> ISSUER_TAG = new OutputTag<Row>("ISSUER") {};

    /** 证书主体顶点 */
    private static final OutputTag<Row> SUBJECT_TAG = new OutputTag<Row>("SUBJECT") {};

    /**
     * 初始化方法
     * 在算子初始化时调用，用于初始化公共资源
     *
     * @param parameters 配置参数
     * @throws Exception 如果初始化失败
     */
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 初始化公共后缀列表
        if (factory == null) {
            factory = new PublicSuffixListFactory();
            suffixList = factory.build();
        }
    }

    /**
     * 获取基础域名
     * 从域名中提取注册域名
     *
     * @param domain 域名
     * @return 基础域名，如果无法获取则返回null
     */
    protected String getRegistrableDomain(String domain) {
        if (domain == null || domain.isEmpty()) {
            return null;
        }

        try {
            // 使用getRegistrableDomain方法获取注册域名
            String registrableDomain = suffixList.getRegistrableDomain(domain);
            if (registrableDomain != null) {
                return registrableDomain;
            }
        } catch (Exception e) {
            log.warn("获取可注册域名失败, domain: {}, error: {}", domain, e.getMessage());
        }

        return null;
    }
}
