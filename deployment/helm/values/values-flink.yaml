# values-flink.yaml - Flink配置文件
# 包含Flink配置和作业信息

# Flink配置
infrastructure:
  flink:
    enabled: true
    # 部署模式配置 (native或standalone)
    mode: native
    # 存储配置 - 基本信息
    storage:
      size: "20Gi"
    storageClass: "standard"
    # Flink Kubernetes Operator启用标志 - 详细配置请参考values-operators.yaml
    # 注意：实际的Operator配置已移至values-operators.yaml文件

    # Flink应用程序配置 - 用于生成FlinkDeployment资源
    configuration:
      # Savepoint相关配置
      savepoint:
        interval: "6h"
        history:
          maxCount: "3"
          maxAge: "24h"
        cleanup:
          enabled: "true"
        disposeOnDelete: "true"
        # MinIO 存储配置
        storage:
          type: "s3"
          dir: "s3://flink-savepoints/"
          s3:
            endpoint: "http://minio.{{ .Release.Namespace }}.svc.cluster.local:9000"
            pathStyleAccess: "true"

      # Checkpoint相关配置
      checkpoint:
        interval: "10000"
        mode: "EXACTLY_ONCE"
        min-pause: "5000"
        tolerable-failed-checkpoints: "3"
        externalized-checkpoint-retention: "RETAIN_ON_CANCELLATION"
        unaligned:
          enabled: "true"
        # MinIO 存储配置
        storage:
          type: "s3"
          dir: "s3://flink-checkpoints/"
          s3:
            endpoint: "http://minio.{{ .Release.Namespace }}.svc.cluster.local:9000"
            pathStyleAccess: "true"

      # 重启策略
      restart:
        strategy: "fixed-delay"
        attempts: "3"
        delay: "10s"

      # 高可用性
      highAvailability:
        enabled: true
        type: "org.apache.flink.kubernetes.highavailability.KubernetesHaServicesFactory"

      # 状态后端配置
      state:
        # 使用 RocksDB 状态后端
        backend: "rocksdb"
        # 状态后端存储目录
        checkpoints.dir: "s3://flink-checkpoints/"
        # 允许未恢复的状态
        allowNonRestoredState: "true"
        # RocksDB 配置
        rocksdb:
          # 启用增量检查点
          incremental: "true"
          # 本地存储目录
          localdir: "/opt/flink/rocksdb"

      # Kubernetes相关配置
      kubernetes:
        imagePullPolicy: "IfNotPresent"
        # 自动恢复配置
        operator:
          # 启用从最近的savepoint自动恢复
          jobUpgradeLastStateFallbackEnabled: true
          # 启用原地扩缩容
          jobUpgradeInplaceScalingEnabled: true
          # 启用集群健康检查
          clusterHealthCheckEnabled: true

      # Java 17+ 支持
      javaOptions: "--add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED"

    # Flink作业配置
    jobs:
      graph-builder:
        className: "com.geeksec.nta.graph.pipeline.GraphBuilderPipeline"
        parallelism: 4
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        image:
          repository: nta/graph-builder
          tag: "latest"
        config:
          kafka:
            groupId: "meta-nebula-01"
            topic: "json_meta"
            modelStateChangedTopic: "model_state_changed"
            modelStateChangedGroupId: "model-state-changed-group"
            outputTopic: "alarmOutput"
            certTopic: "certfile"
            certGroupId: "certfile-01"
            # 协议元数据Kafka主题配置
            topicJsonConnect: "connect-info"
            topicJsonHttp: "http-info"
            topicJsonDns: "dns-info"
            topicJsonSsl: "ssl-info"
            topicJsonSsh: "ssh-info"
            topicJsonRlogin: "rlogin-info"
            topicJsonTelnet: "telnet-info"
            topicJsonRdp: "rdp-info"
            topicJsonVnc: "vnc-info"
            topicJsonXdmcp: "xdmcp-info"
            topicJsonNtp: "ntp-info"
            topicJsonIcmp: "icmp-info"
          parallelism:
            kafkaSource: 4
            parsing: 128
            elasticsearchSink: 24
            kafkaSink: 8
            kafkaJsonSink: 16
            connectInfoUpdate: 16

      threat-detector:
        enabled: true
        className: "com.geeksec.sessionthreatdetector.job.ThreatDetectionJob"
        parallelism: 4
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        image:
          repository: nta/session-threat-detector
          tag: "latest"
        config:
          kafka:
            groupId: "session-threat-detector"
            topic: "json_meta"
          parallelism:
            kafkaSource: 4
            processing: 16
            elasticsearchSink: 8

      certificate-analyzer:
        enabled: true
        className: "com.geeksec.nta.pipeline.CertificateAnalysisPipeline"
        parallelism: 4
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        image:
          repository: nta/certificate-analyzer
          tag: "latest"
        config:
          kafka:
            groupId: "cert-analyzer"
            topic: "certfile"
          # MinIO 配置 - 用于存储和读取证书文件
          minio:
            enabled: true
            bucket: "{{ .Values.global.minio.defaultBucket }}"
            endpoint: "{{ .Values.global.minio.endpoint }}"
            pathPrefix: "certificates/"
          parallelism:
            kafkaSource: 4
            processing: 16
            elasticsearchSink: 8

      traffic-etl-processor:
        enabled: true
        className: "com.geeksec.nta.trafficetl.job.TrafficEtlPipeline"
        parallelism: 4
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        image:
          repository: nta/traffic-etl-processor
          tag: "latest"
        # 元数据资源挂载配置
        volumes:
          - name: metadata-resources
            configMap:
              name: "{{ include \"nta.fullname\" . }}-metadata-resources"
          - name: malicious-domains
            configMap:
              name: "{{ include \"nta.fullname\" . }}-malicious-domains"
          - name: geoip-city
            configMap:
              name: "{{ include \"nta.fullname\" . }}-geoip-city"
          - name: geoip-asn
            configMap:
              name: "{{ include \"nta.fullname\" . }}-geoip-asn"
        volumeMounts:
          - name: metadata-resources
            mountPath: /opt/flink/resources/metadata
            readOnly: true
          - name: malicious-domains
            mountPath: /opt/flink/resources/malicious-domains
            readOnly: true
          - name: geoip-city
            mountPath: /opt/flink/resources/geoip/city
            readOnly: true
          - name: geoip-asn
            mountPath: /opt/flink/resources/geoip/asn
            readOnly: true
        config:
          kafka:
            groupId: "traffic-etl-processor"
            topicMeta: "meta"
            topicJson: "json_meta"
            # 协议元数据Kafka主题配置
            topicJsonConnect: "connect-info"
            topicJsonHttp: "http-info"
            topicJsonDns: "dns-info"
            topicJsonSsl: "ssl-info"
            topicJsonSsh: "ssh-info"
            topicJsonRlogin: "rlogin-info"
            topicJsonTelnet: "telnet-info"
            topicJsonRdp: "rdp-info"
            topicJsonVnc: "vnc-info"
            topicJsonXdmcp: "xdmcp-info"
            topicJsonNtp: "ntp-info"
            topicJsonIcmp: "icmp-info"
          # 证书提取配置
          certificateExtraction:
            enabled: false  # 默认禁用，按需启用
            parallelism: 2
          # 证书相关主题配置
          topics:
            certificateFiles: "certfile"
            systemCertificates: "certfile_system"
          # MinIO 配置 - 用于存储和读取数据
          minio:
            enabled: true
            bucket: "{{ .Values.global.minio.defaultBucket }}"
            endpoint: "{{ .Values.global.minio.endpoint }}"
            # 凭据通过环境变量传入，这里不需要配置
            pathPrefix: "data-warehouse/"
          # 元数据资源路径配置
          metadata:
            resourcePath: "/opt/flink/resources"
            geoipPath: "/opt/flink/resources/geoip"
            maliciousDomainsPath: "/opt/flink/resources/malicious-domains"
          parallelism:
            kafkaSource: 4
            parsing: 64
            kafkaSink: 8
            kafkaJsonSink: 16
            connectInfoUpdate: 16
            dorisSink: 8

      alarm-processor:
        enabled: true
        className: "com.geeksec.alarmprocessor.job.AlarmProcessorJob"
        parallelism: 4
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        image:
          repository: nta/alarm-processor
          tag: "latest"
        config:
          # Kafka 配置
          kafka:
            groupId: "alarm-processor"
            inputTopic: "alarm-events"
            outputTopic: "processed-alarms"
            notificationTopic: "alarm-notifications"
          # PostgreSQL 配置
          postgresql:
            database: "nta"
            table: "alarm_records"
          # 处理配置
          processing:
            deduplication:
              enabled: true
              timeWindowMs: 60000
              maxCacheSize: 10000
            formatting:
              enabled: true
              includeReasonAnalysis: true
              includeHandlingSuggestions: true
            attackChain:
              enabled: true
              correlationWindowMs: 300000
              maxCacheSize: 15000
            batch:
              enabled: true
              maxBatchSize: 50
              maxWaitTimeMs: 30000
          # 输出配置
          output:
            notification:
              enabled: true
              batchSize: 50
              lingerMs: 1000
          # 监控配置
          monitoring:
            enabled: true
            metricsInterval: 30000
          parallelism:
            kafkaSource: 4
            processing: 8
            postgresqlSink: 2
            notificationSink: 2

      alarm-cdc-sync:
        enabled: true
        className: "com.geeksec.alarm.cdc.AlarmCdcSyncJob"
        parallelism: 2
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        image:
          repository: nta/alarm-cdc-sync
          tag: "latest"
        config:
          # PostgreSQL CDC 配置
          postgresql:
            slot: "alarm_cdc_slot"
            snapshotMode: "initial"
            decodingPlugin: "pgoutput"
          # Doris 配置
          doris:
            database: "nta"
            table: "ods_alarm_log"
            labelPrefix: "alarm-cdc"
          # 同步配置
          sync:
            batchSize: 1000
            batchInterval: 5000
            checkpointInterval: 60000
          parallelism:
            source: 1
            transform: 2
            sink: 1

# Flink 作业配置（独立于infrastructure.flink.jobs）
flinkJobs:
  alarmCdcSync:
    enabled: true
    parallelism: 2
    resources:
      requests:
        memory: "512Mi"
        cpu: "250m"
      limits:
        memory: "1Gi"
        cpu: "500m"

  sessionLabelsCdcSync:
    enabled: true
    parallelism: 2
    resources:
      requests:
        memory: "512Mi"
        cpu: "250m"
      limits:
        memory: "1Gi"
        cpu: "500m"

  certLabelsCdcSync:
    enabled: true
    parallelism: 2
    resources:
      requests:
        memory: "512Mi"
        cpu: "250m"
      limits:
        memory: "1Gi"
        cpu: "500m"
