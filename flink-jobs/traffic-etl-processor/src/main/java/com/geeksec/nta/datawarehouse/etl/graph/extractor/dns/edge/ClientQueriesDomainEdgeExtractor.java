package com.geeksec.nta.datawarehouse.etl.graph.extractor.dns.edge;

import com.geeksec.common.infrastructure.network.DomainUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.common.toolkit.crypto.HashUtils;
import com.geeksec.common.utils.net.DomainUtils;
import com.geeksec.nta.datawarehouse.etl.constant.FieldConstants;
import com.geeksec.nta.datawarehouse.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.datawarehouse.etl.graph.extractor.dns.DNSEdgeExtractor;
import com.geeksec.nta.datawarehouse.sink.tag.NebulaGraphOutputTag;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.util.*;

/**
 * @Description null.java
 * @Date 9:30$ 2025/6/18$
 **/
@Slf4j
public class ClientQueriesDomainEdgeExtractor extends BaseEdgeExtractor {
    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.CLIENT_QUERIES_DOMAIN_TAG;
    }

    /**
     * DNS查询域名 (源IP -> DOMAIN)
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        // 获取源IP和域名
        String sIP = value.getField(FieldConstants.FIELD_SRC_IP).toString();
        String domain = value.getField(FieldConstants.FIELD_DNS_DOMAIN).toString();
        String dnsQueStr = value.getField(FieldConstants.FIELD_DNS_QUERY).toString();
        if (dnsQueStr.isEmpty() || DomainUtils.isValidDomain(domain)) {
            return Collections.emptyList();
        }

        // 获取Query和Answer中的type信息
        int queryType = DNSEdgeExtractor.extractQueryType(value);
        int answerType = DNSEdgeExtractor.extractAnswerType(value);
        if (queryType == -1){
            return Collections.emptyList();
        }

        domain = DomainUtils.formatDomain(domain);
        return List.of(Row.of(sIP, domain,
                0, // rank暂定0
                queryType, answerType
            ));
    }
}
