package com.geeksec.nta.trafficetl.etl.ods.processor;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import com.geeksec.proto.ZMPNMsg;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书提取器
 * 从SSL协议元数据中提取原始证书数据
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class CertificateExtractor extends ProcessFunction<ZMPNMsg.JKNmsg, ZMPNMsg.JKNmsg> {

    /**
     * 证书数据输出标签
     */
    public static final OutputTag<byte[]> CERTIFICATE_OUTPUT_TAG = new OutputTag<byte[]>("certificate-output",
            TypeInformation.of(byte[].class));

    @Override
    public void processElement(ZMPNMsg.JKNmsg msg, Context context, Collector<ZMPNMsg.JKNmsg> collector)
            throws Exception {
        // 继续传递原始消息到主流
        collector.collect(msg);

        // 检查是否包含SSL消息
        if (!msg.hasSsl()) {
            return;
        }

        ZMPNMsg.ssl_msg sslMsg = msg.getSsl();

        // 提取客户端证书链
        if (sslMsg.getSslCertCRawCount() > 0) {
            log.debug("提取到 {} 个客户端证书", sslMsg.getSslCertCRawCount());
            for (int i = 0; i < sslMsg.getSslCertCRawCount(); i++) {
                byte[] certData = sslMsg.getSslCertCRaw(i).toByteArray();
                if (certData != null && certData.length > 0) {
                    context.output(CERTIFICATE_OUTPUT_TAG, certData);
                    log.debug("输出客户端证书 {}, 大小: {} 字节", i + 1, certData.length);
                }
            }
        }

        // 提取服务端证书链
        if (sslMsg.getSslCertSRawCount() > 0) {
            log.debug("提取到 {} 个服务端证书", sslMsg.getSslCertSRawCount());
            for (int i = 0; i < sslMsg.getSslCertSRawCount(); i++) {
                byte[] certData = sslMsg.getSslCertSRaw(i).toByteArray();
                if (certData != null && certData.length > 0) {
                    context.output(CERTIFICATE_OUTPUT_TAG, certData);
                    log.debug("输出服务端证书 {}, 大小: {} 字节", i + 1, certData.length);
                }
            }
        }
    }
}
