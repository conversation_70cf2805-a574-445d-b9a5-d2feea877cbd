package com.geeksec.nta.datawarehouse.etl.dim.function;

import com.geeksec.common.toolkit.crypto.HashUtils;
import com.geeksec.nta.datawarehouse.etl.constant.FieldConstants;
import com.geeksec.nta.datawarehouse.etl.dim.DimensionProcessor;
import lombok.extern.slf4j.Slf4j;
import nl.basjes.parse.useragent.UserAgent;
import nl.basjes.parse.useragent.UserAgentAnalyzer;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import java.util.Map;

/**
 * UA OS Device维度表处理函数，用于生成符合dim_ua os device表结构的维度数据
 * <p>
 * 使用Flink Backend State进行缓存管理，支持TTL自动清理
 *
 * <AUTHOR>
 */
@Slf4j
public class UAPParseDimensionTableFunction extends ProcessFunction<Row, Row> {
    private static final long serialVersionUID = 1L;

    /**
     * UA维度数据输出标签
     */
    public static final OutputTag<Row> UA_DIM_TAG = new OutputTag<Row>("ua-dimension") {
    };
    public static final OutputTag<Row> UA_VERTEX_TAG = new OutputTag<Row>("ua-tag") {
    };
    /**
     * OS维度数据输出标签
     */
    public static final OutputTag<Row> OS_DIM_TAG = new OutputTag<Row>("os-dimension") {
    };
    public static final OutputTag<Row> OS_VERTEX_TAG = new OutputTag<Row>("os-tag") {
    };
    /**
     * 设备维度数据输出标签
     */
    public static final OutputTag<Row> DEVICE_DIM_TAG = new OutputTag<Row>("device-dimension") {
    };
    public static final OutputTag<Row> DEVICE_VERTEX_TAG = new OutputTag<Row>("device-tag") {
    };
    public static UserAgentAnalyzer analyzer = UserAgentAnalyzer
            .newBuilder()
            .withCache(10000) // 设置缓存大小
            .build();

    // ua、os、设备维度信息缓存
    private transient MapState<String, Map<String, Object>> uaParseDimensionCache;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        MapStateDescriptor<String, Map<String, Object>> mapStateDescriptor = new MapStateDescriptor<>("ua-parse-dimension-cache",
                TypeInformation.of(new TypeHint<String>() {
                }), TypeInformation.of(new TypeHint<Map<String, Object>>() {
        }));
        uaParseDimensionCache = getRuntimeContext().getMapState(mapStateDescriptor);
    }

    @Override
    public void processElement(Row value, Context ctx, Collector<Row> out) {
        try {
            Map<String, Object> httpClient = (Map<String, Object>) value.getField(FieldConstants.FIELD_HTTP_CLIENT_KV);
            String ua = httpClient.get(FieldConstants.FIELD_HTTP_UA).toString();
            if (ua == null || ua.trim().isEmpty()) {
                log.warn("UA为空");
                out.collect(value);
                return;
            }

            // 解析User-Agent
            UserAgent agent = analyzer.parse(ua);
            // 更新时间记录缓存
            DimensionProcessor.updateTimeStateMap(uaParseDimensionCache, ua);
            // 输出UA、OS、Device维度数据
            ctx.output(UA_DIM_TAG, createUADimensionRow(ua, agent));
            ctx.output(UA_VERTEX_TAG, createUAVertexRow(ua, agent));

            if (!agent.getValue("OperatingSystemName").contains("Unknown")) {
                ctx.output(OS_DIM_TAG, createOSDimensionRow(ua, agent));
                ctx.output(OS_VERTEX_TAG, createOSVertexRow(ua, agent));
            }

            if (!agent.getValue("DeviceName").contains("Unknown")) {
                ctx.output(DEVICE_DIM_TAG, createDeviceDimensionRow(ua, agent));
                ctx.output(DEVICE_VERTEX_TAG, createDeviceVertexRow(ua, agent));
            }

            out.collect(value);
        } catch (Exception e) {
            log.error("处理UA OS Device维度数据时发生错误: {}", e.getMessage(), e);
            out.collect(value);
        }
    }

    /**
     * 创建符合维度表结构的UA维度记录
     *
     * @param ua    UA字符串
     * @param agent UA解析结果
     * @return 符合dim_ua表结构的Row
     */
    private Row createUADimensionRow(String ua, UserAgent agent) {
        Row dimensionRow = Row.withNames();
        dimensionRow.setField("id", HashUtils.md5(ua));
        dimensionRow.setField("ua", ua);
        dimensionRow.setField("browser_engine", agent.getValue("BrowserName"));
        dimensionRow.setField("browser_version", agent.getValue("BrowserVersion"));
        String agentClass = agent.getValue("AgentClass");
        boolean isBot = agentClass != null &&
                (agentClass.equals("Robot") || agentClass.equals("Cloud") || agentClass.equals("Special"));
        dimensionRow.setField("is_bot", isBot ? 1 : 0);
        dimensionRow.setField("bot_name", isBot ? agent.getValue("AgentName") : null);
        dimensionRow.setField("bot_category", isBot ? agentClass : null);
        dimensionRow.setField("layout_engine", agent.getValue("LayoutEngineName"));
        dimensionRow.setField("layout_engine_version", agent.getValue("LayoutEngineVersion"));
        dimensionRow.setField("agent_class", agentClass);
        dimensionRow.setField("threat_score", 0);
        dimensionRow.setField("trust_score", 100);
        DimensionProcessor.insertTimeStateMap(uaParseDimensionCache, ua, dimensionRow);

        return dimensionRow;
    }

    /**
     * 创建符合UA TAG结构的UA维度记录
     *
     * @param ua    UA字符串
     * @param agent UA解析结果
     * @return 符合UA TAG定义结构的Row
     */
    private Row createUAVertexRow(String ua, UserAgent agent) {
        String agentClass = agent.getValue("AgentClass");
        boolean isBot = agentClass != null &&
                (agentClass.equals("Robot") || agentClass.equals("Cloud") || agentClass.equals("Special"));

        return Row.of(agent.getValue("AgentName") + "_" + agent.getValue("OperatingSystemName") + "_" + agent.getValue("DeviceName"), // vid
                ua, "", agent.getValue("BrowserName"), agent.getValue("BrowserVersion"),
                isBot, isBot ? agent.getValue("AgentName") : "", isBot ? agentClass : "",
                agent.getValue("LayoutEngineName"), agent.getValue("LayoutEngineVersion"),
                agent.getValue("AgentClass"), 0, 100);
    }

    /**
     * 创建符合维度表结构的OS维度记录
     *
     * @param agent
     * @return 符合dim_os表结构的Row
     */
    private Row createOSDimensionRow(String ua, UserAgent agent) {
        Row dimensionRow = Row.withNames();
        dimensionRow.setField("os_name", agent.getValue("OperatingSystemName"));
        dimensionRow.setField("os_version", agent.getValue("OperatingSystemVersion"));
        dimensionRow.setField("os_major_version", agent.getValue("OperatingSystemVersionMajor"));
        dimensionRow.setField("os_minor_version", "");
        dimensionRow.setField("os_class", agent.getValue("OperatingSystemClass"));
        dimensionRow.setField("threat_score", 0);
        dimensionRow.setField("trust_score", 100);
        DimensionProcessor.insertTimeStateMap(uaParseDimensionCache, ua, dimensionRow);

        return dimensionRow;
    }

    /**
     * 创建符合OS TAG结构的OS维度记录
     *
     * @param agent
     * @return 符合OS TAG结构的Row
     */
    private Row createOSVertexRow(String ua, UserAgent agent) {
        return Row.of(agent.getValue("OperatingSystemName"), // vid
                agent.getValue("OperatingSystemName"), agent.getValue("OperatingSystemVersion"), "",
                agent.getValue("OperatingSystemClass"), 0, 100
        );
    }

    /**
     * 创建设备维度记录
     *
     * @param agent
     * @return 符合dim_device表结构的Row
     */
    private Row createDeviceDimensionRow(String ua, UserAgent agent) {
        Row dimensionRow = Row.withNames();

        // 设置主键字段
        dimensionRow.setField("device_name", agent.getValue("DeviceName"));

        // 设置设备信息字段
        dimensionRow.setField("device_brand", agent.getValue("DeviceBrand"));
        dimensionRow.setField("device_model", agent.getValue("DeviceFirmwareVersion"));
        dimensionRow.setField("device_category", agent.getValue("DeviceClass"));
        dimensionRow.setField("threat_score", 0);
        dimensionRow.setField("trust_score", 100);

        // 设置时间字段
        DimensionProcessor.insertTimeStateMap(uaParseDimensionCache, ua, dimensionRow);

        return dimensionRow;
    }

    /**
     * 创建设备 TAG记录
     *
     * @param agent
     * @return 符合DEVICE TAG结构的Row
     */
    private Row createDeviceVertexRow(String ua, UserAgent agent) {
        return Row.of(agent.getValue("DeviceName"), // vid
                agent.getValue("DeviceName"), agent.getValue("DeviceBrand"), agent.getValue("DeviceFirmwareVersion"),
                agent.getValue("DeviceClass"), 0, 100
        );
    }
}
