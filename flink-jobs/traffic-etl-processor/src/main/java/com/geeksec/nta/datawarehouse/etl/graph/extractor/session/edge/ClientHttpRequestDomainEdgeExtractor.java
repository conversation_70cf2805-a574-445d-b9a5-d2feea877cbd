package com.geeksec.nta.datawarehouse.etl.graph.extractor.session.edge;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import com.geeksec.common.infrastructure.network.DomainUtils;
import com.geeksec.nta.datawarehouse.etl.constant.FieldConstants;
import com.geeksec.nta.datawarehouse.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.datawarehouse.sink.tag.NebulaGraphOutputTag;
import com.geeksec.proto.ZMPNMsg;

/**
 * @Description null.java
 * @Date 9:30$ 2025/6/18$
 **/
public class ClientHttpRequestDomainEdgeExtractor extends BaseEdgeExtractor {
    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.CLIENT_HTTP_REQUESTS_DOMAIN_TAG;
    }

    /**
     * HTTP客户端访问域名 (sIP -> DOMAIN)
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        List<Row> edges = new ArrayList<>();
        // 从HTTP信息中获取域名
        List<ZMPNMsg.single_http> httpInfoList = (List<ZMPNMsg.single_http>) value.getField(FieldConstants.SESSION_HTTP_LIST);
        if (CollectionUtils.isNotEmpty(httpInfoList)) {
            for (ZMPNMsg.single_http singleHttp : httpInfoList) {
                String response = singleHttp.getResponse();
                if (!StringUtil.isNullOrEmpty(response) && response.contains("200")) {
                    String httpDomainAddr = singleHttp.getHost();
                    // 获取服务端IP
                    String serverIp = value.getField(FieldConstants.FIELD_DST_IP).toString();
                    String clientIp = value.getField(FieldConstants.FIELD_DST_IP).toString();
                    if (DomainUtils.isValidDomain(httpDomainAddr) && !httpDomainAddr.equals(serverIp)) {
                        httpDomainAddr = DomainUtils.formatDomain(httpDomainAddr);
                        edges.add(Row.of(clientIp, httpDomainAddr,
                                0 // rank 暂定0
                        ));
                    }
                }
            }
        }
        return edges;
    }
}
