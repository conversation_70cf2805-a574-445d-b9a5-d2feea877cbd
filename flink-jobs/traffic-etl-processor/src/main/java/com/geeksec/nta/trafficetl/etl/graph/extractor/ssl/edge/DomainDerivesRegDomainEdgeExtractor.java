package com.geeksec.nta.trafficetl.etl.graph.extractor.ssl.edge;

import java.util.Collections;
import java.util.List;

import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import com.geeksec.common.infrastructure.network.DomainUtils;
import com.geeksec.nta.trafficetl.etl.constant.FieldConstants;
import com.geeksec.nta.trafficetl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.trafficetl.sink.tag.NebulaGraphOutputTag;

/**
 * @Description null.java
 * @Date 17:11$ 2025/6/17$
 **/
public class DomainDerivesRegDomainEdgeExtractor extends BaseEdgeExtractor {
    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.DOMAIN_DERIVES_FROM_REGISTRABLE_DOMAIN_TAG;
    }

    /**
     * 域名属于锚域名 (DOMAIN -> FDOMAIN)
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        String sni = value.getField(FieldConstants.FIELD_SSL_HELLO_C_SERVERNAME).toString();
        if (!DomainUtils.isValidDomain(sni)) {
            return Collections.emptyList();
        }

        if (sni.contains(":")) {
            sni = sni.split("\\:")[0];
        }

        String fDomain = getBaseDomain(sni);
        sni = DomainUtils.formatDomain(sni);
        fDomain = DomainUtils.formatDomain(fDomain);

        if (fDomain == null){
            return Collections.emptyList();
        }

        return List.of(Row.of(sni, fDomain,
                0 // rank暂定0
        ));
    }
}
