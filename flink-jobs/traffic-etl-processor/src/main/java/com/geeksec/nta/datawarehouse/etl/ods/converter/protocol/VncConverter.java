package com.geeksec.nta.datawarehouse.etl.ods.converter.protocol;

import com.geeksec.common.toolkit.time.TimeUtils;
import com.geeksec.nta.datawarehouse.etl.constant.FieldConstants;
import com.geeksec.nta.datawarehouse.etl.ods.converter.common.AbstractProtobufMessageConverter;
import com.geeksec.nta.datawarehouse.etl.ods.tag.MessageOutputTag;
import com.geeksec.proto.ZMPNMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * Converter for VNC protocol messages.
 * Maps VNC protocol data from protobuf messages to Doris
 * ods_vnc_protocol_metadata table format.
 * This converter is aligned with the latest ods_vnc_protocol_metadata table
 * schema.
 *
 * <AUTHOR> Team
 */
@Slf4j
public class VncConverter extends AbstractProtobufMessageConverter {

    @Override
    protected Row convertMessage(ZMPNMsg.JKNmsg msg) {
        if (!msg.hasVnc()){
            log.warn("VNC protocol message is empty.");
            return null;
        }

        Row row = Row.withNames();
        ZMPNMsg.vnc_msg vnc = msg.getVnc();
        if (vnc.hasCommMsg()){
            // Common fields from Comm_msg
            enrichComMsg(row, vnc.getCommMsg());
        }

        // 服务端帧缓冲区信息 (vnc_sever_fb_msg)
        if (vnc.hasServerFb()) {
            ZMPNMsg.vnc_sever_fb_msg serverFb = vnc.getServerFb();
            row.setField(FieldConstants.FIELD_FB_WIDTH, serverFb.getFbWidth());
            row.setField(FieldConstants.FIELD_FB_HEIGHT, serverFb.getFbHeight());
            row.setField(FieldConstants.FIELD_BITS_PER_PIXEL, serverFb.getBitsPerPixel());
            row.setField(FieldConstants.FIELD_DEPTH, serverFb.getDepth());
            row.setField(FieldConstants.FIELD_BIG_ENDIAN_FLAG, serverFb.getBigEndianFlag());
            row.setField(FieldConstants.FIELD_TRUE_COLOR_FLAG, serverFb.getTrueColorFlag());
            row.setField(FieldConstants.FIELD_RED_MAXIMUM, serverFb.getRedMaximum());
            row.setField(FieldConstants.FIELD_GREEN_MAXIMUM, serverFb.getGreenMaximum());
            row.setField(FieldConstants.FIELD_BLUE_MAXIMUM, serverFb.getBlueMaximum());
            row.setField(FieldConstants.FIELD_RED_SHIFT, serverFb.getRedShift());
            row.setField(FieldConstants.FIELD_GREEN_SHIFT, serverFb.getGreenShift());
            row.setField(FieldConstants.FIELD_BLUE_SHIFT, serverFb.getBlueShift());
            row.setField(FieldConstants.FIELD_DESKTOP_NAME, serverFb.getDesktopName());
        }

        // 客户端编码设置 (vnc_client_set_encoding_msg)
        if (vnc.hasEncoding()) {
            row.setField(FieldConstants.FIELD_ENCODING_TYPES,
                    convertProtoListToJavaList(vnc.getEncoding().getEncodingTypesList()));
        }

        // 客户端像素格式设置 (vnc_client_set_pixel_format_msg)
        if (vnc.hasPixelFormat()) {
            ZMPNMsg.vnc_client_set_pixel_format_msg pixelFormat = vnc.getPixelFormat();
            row.setField(FieldConstants.FIELD_CLIENT_BITS_PER_PIXEL, pixelFormat.getBitsPerPixel());
            row.setField(FieldConstants.FIELD_CLIENT_DEPTH, pixelFormat.getDepth());
            row.setField(FieldConstants.FIELD_CLIENT_BIG_ENDIAN_FLAG, pixelFormat.getBigEndianFlag());
            row.setField(FieldConstants.FIELD_CLIENT_TRUE_COLOR_FLAG, pixelFormat.getTrueColorFlag());
            row.setField(FieldConstants.FIELD_CLIENT_RED_MAXIMUM, pixelFormat.getRedMaximum());
            row.setField(FieldConstants.FIELD_CLIENT_GREEN_MAXIMUM, pixelFormat.getGreenMaximum());
            row.setField(FieldConstants.FIELD_CLIENT_BLUE_MAXIMUM, pixelFormat.getBlueMaximum());
            row.setField(FieldConstants.FIELD_CLIENT_RED_SHIFT, pixelFormat.getRedShift());
            row.setField(FieldConstants.FIELD_CLIENT_GREEN_SHIFT, pixelFormat.getGreenShift());
            row.setField(FieldConstants.FIELD_CLIENT_BLUE_SHIFT, pixelFormat.getBlueShift());
        }

        // 协议版本信息
        row.setField(FieldConstants.FIELD_SERVER_PROTOCOL_VER, vnc.getServerProtocolVer());
        row.setField(FieldConstants.FIELD_CLIENT_PROTOCOL_VER, vnc.getClientProtocolVer());

        // 安全类型信息
        row.setField(FieldConstants.FIELD_SERVER_SECURE_TYPE_SUPPORTED,
                convertProtoListToJavaList(vnc.getServerSecureTypeSupportedList()));
        row.setField(FieldConstants.FIELD_CLIENT_SECURE_TYPE_SELECTED, vnc.getClientSecureTypeSelected());

        // 认证信息
        row.setField(FieldConstants.FIELD_SERVER_AUTHENTICATION_CHALLENGE,
                bytesToBase64String(vnc.getServerAuthenticationChallenge()));
        row.setField(FieldConstants.FIELD_CLIENT_AUTHENTICATION_RESPONSE,
                bytesToBase64String(vnc.getClientAuthenticationResponse()));
        row.setField(FieldConstants.FIELD_AUTHENTICATION_RESULT, vnc.getAuthenticationResult());

        // 其他信息
        row.setField(FieldConstants.FIELD_SHARE_DSKTP_FLAG, vnc.getShareDsktpFlag());

        return row;
    }

    @Override
    public OutputTag<Row> getOutputTag() {
        return MessageOutputTag.VNC_STREAM;
    }

}
