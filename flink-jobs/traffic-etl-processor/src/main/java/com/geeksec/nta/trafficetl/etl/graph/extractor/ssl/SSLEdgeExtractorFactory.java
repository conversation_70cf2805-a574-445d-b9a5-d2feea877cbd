package com.geeksec.nta.trafficetl.etl.graph.extractor.ssl;

import com.geeksec.nta.trafficetl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.trafficetl.etl.graph.extractor.ssl.edge.*;

import java.util.Arrays;
import java.util.List;

/**
 * @Description null.java
 * @Date 17:00$ 2025/6/17$
 **/
public class SSLEdgeExtractorFactory {
    public static List<BaseEdgeExtractor> getEdgeExtractors() {
        return Arrays.asList(
                new CertServersSniEdgeExtractor(),
                new ClientReceivesCertEdgeExtractor(),
                new ClientTLSRequestsDomainEdgeExtractor(),
                new DomainDerivesRegDomainEdgeExtractor(),
                new FingerprintAppearsWithDomainEdgeExtractor(),
                new IPPresentsFingerprintEdgeExtractor(),
                new IPProvidesCertEdgeExtractor(),
                new ServerTLSHostsDomainEdgeExtractor()
        );
    }
}
