package com.geeksec.nta.trafficetl.etl.graph.extractor.dns.edge;

import com.geeksec.common.infrastructure.network.DomainUtils;
import com.geeksec.common.utils.net.DomainUtils;
import com.geeksec.nta.trafficetl.etl.constant.FieldConstants;
import com.geeksec.nta.trafficetl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.trafficetl.etl.graph.extractor.dns.DNSEdgeExtractor;
import com.geeksec.nta.trafficetl.sink.tag.NebulaGraphOutputTag;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.util.Collections;
import java.util.List;

/**
 * @Description null.java
 * @Date 9:30$ 2025/6/18$
 **/
@Slf4j
public class DnsServerResolvesDomainEdgeExtractor extends BaseEdgeExtractor {
    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.DNS_SERVER_RESOLVES_DOMAIN_TAG;
    }

    /**
     * DNS服务器解析域名 (目的IP -> DOMAIN)
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        // 获取目的IP和域名
        String dIP = value.getField(FieldConstants.FIELD_DST_IP).toString();
        String domain = value.getField(FieldConstants.FIELD_DNS_DOMAIN).toString();
        if (!DomainUtils.isValidDomain(domain)) {
            return Collections.emptyList();
        }

        // 获取queryType和answerType
        int queryType = DNSEdgeExtractor.extractQueryType(value);
        int answerType = DNSEdgeExtractor.extractAnswerType(value);

        domain = DomainUtils.formatDomain(domain);
        return List.of(Row.of(dIP, domain,
                0, // rank暂定0
                queryType, answerType
        ));
    }
}
