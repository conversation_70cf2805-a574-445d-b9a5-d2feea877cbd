package com.geeksec.common.core.constants;

/**
 * 配置常量类
 * 定义所有模块通用的配置项常量
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public final class ConfigConstants {

    private ConfigConstants() {
        // 工具类，禁止实例化
    }

    // Kafka相关配置
    public static final String KAFKA_BOOTSTRAP_SERVERS = "kafka.bootstrap.servers";
    public static final String KAFKA_GROUP_ID = "kafka.group.id";
    public static final String KAFKA_TOPIC_META = "kafka.topic.meta";
    public static final String KAFKA_OUTPUT_TOPIC = "kafka.output.topic";
    public static final String KAFKA_ORDER_TOPIC = "kafka.order.topic";
    public static final String KAFKA_ORDER_GROUP_ID = "kafka.order.group.id";
    public static final String KAFKA_ALERT_TOPIC = "kafka.alert.topic";
    public static final String KAFKA_ALERT_GROUP_ID = "kafka.alert.group.id";
    
    // Kafka Topic 配置
    public static final String KAFKA_TOPIC_CONNECT = "kafka.topic.connect";
    public static final String KAFKA_TOPIC_HTTP = "kafka.topic.http";
    public static final String KAFKA_TOPIC_DNS = "kafka.topic.dns";
    public static final String KAFKA_TOPIC_SSL = "kafka.topic.ssl";
    public static final String KAFKA_TOPIC_SSH = "kafka.topic.ssh";
    public static final String KAFKA_TOPIC_RLOGIN = "kafka.topic.rlogin";
    public static final String KAFKA_TOPIC_TELNET = "kafka.topic.telnet";
    public static final String KAFKA_TOPIC_RDP = "kafka.topic.rdp";
    public static final String KAFKA_TOPIC_VNC = "kafka.topic.vnc";
    public static final String KAFKA_TOPIC_XDMCP = "kafka.topic.xdmcp";
    public static final String KAFKA_TOPIC_NTP = "kafka.topic.ntp";
    public static final String KAFKA_TOPIC_ICMP = "kafka.topic.icmp";
    public static final String KAFKA_SECURITY_PROTOCOL = "kafka.security.protocol";
    public static final String KAFKA_SASL_MECHANISM = "kafka.sasl.mechanism";
    public static final String KAFKA_SASL_JAAS_CONFIG = "kafka.sasl.jaas.config";
    public static final String KAFKA_CLIENT_USER = "KAFKA_CLIENT_USER";
    public static final String KAFKA_CLIENT_PASSWORD = "KAFKA_CLIENT_PASSWORD";
    // 元数据的kafka配置
    public static final String KAFKA_TOPIC = "kafka.topic";
    // 模型状态变更的kafka配置
    public static final String KAFKA_MODEL_STATE_CHANGED_GROUP_ID = "kafka.modelStateChangedGroupId";
    public static final String KAFKA_MODEL_STATE_CHANGED_TOPIC = "kafka.modelStateChangedTopic";
    // 证书相关配置
    public static final String CERT_KAFKA_TOPIC_NAME = "cert.kafka.topic.name";
    public static final String CERT_KAFKA_GROUP_ID = "cert.kafka.group.id";
    public static final String KAFKA_TOPIC_CERTIFICATE_FILES = "kafka.topic.certificate.files";
    public static final String KAFKA_TOPIC_SYSTEM_CERTIFICATES = "kafka.topic.system.certificates";

    // 证书分析器专用配置
    public static final String CERTIFICATE_ANALYZER_ENABLED = "certificate.analyzer.enabled";
    public static final String CERTIFICATE_ANALYZER_PARALLELISM = "certificate.analyzer.parallelism";
    public static final String CERTIFICATE_ANALYZER_BUFFER_SIZE = "certificate.analyzer.buffer.size";
    public static final String CERTIFICATE_ANALYZER_TIMEOUT_MS = "certificate.analyzer.timeout.ms";
    public static final String CERTIFICATE_ANALYZER_DEBUG_ENABLED = "certificate.analyzer.debug.enabled";
    public static final String CERTIFICATE_ANALYZER_LMDB_ENABLED = "certificate.analyzer.lmdb.enabled";

    // 证书提取配置
    public static final String CERTIFICATE_EXTRACTION_ENABLED = "certificate.extraction.enabled";
    public static final String CERTIFICATE_EXTRACTION_PARALLELISM = "certificate.extraction.parallelism";

    // 证书输出配置
    public static final String CERTIFICATE_OUTPUT_POSTGRESQL_ENABLED = "certificate.output.postgresql.enabled";
    public static final String CERTIFICATE_OUTPUT_NEBULA_ENABLED = "certificate.output.nebula.enabled";
    public static final String CERTIFICATE_ANALYZER_ES_USER_INDEX = "certificate.analyzer.es.user.index";
    public static final String CERTIFICATE_ANALYZER_ES_SYSTEM_INDEX = "certificate.analyzer.es.system.index";

    // Redis相关配置
    public static final String REDIS_HOST = "redis.host";
    public static final String REDIS_PORT = "redis.port";
    public static final String REDIS_PASSWORD = "redis.password";
    public static final String REDIS_TIMEOUT = "redis.timeout";
    public static final String REDIS_POOL_MAX_TOTAL = "redis.pool.max-total";
    public static final String REDIS_POOL_MAX_IDLE = "redis.pool.max-idle";
    public static final String REDIS_POOL_MIN_IDLE = "redis.pool.min-idle";
    
    // Doris 相关配置
    public static final String DORIS_HOST = "doris.host";
    public static final String DORIS_FE_NODES = "doris.fe.nodes";
    public static final String DORIS_DATABASE = "doris.database";
    public static final String DORIS_USERNAME = "DORIS_USERNAME";
    public static final String DORIS_PASSWORD = "DORIS_PASSWORD";
    public static final String DORIS_JDBC_URL = "doris.jdbc.url";
    public static final String DORIS_JDBC_USERNAME = "doris.jdbc.username";
    public static final String DORIS_JDBC_PASSWORD = "doris.jdbc.password";
    public static final String DORIS_JDBC_DRIVER = "doris.jdbc.driver";
    public static final String DORIS_SESSION_TABLE = "doris.session.table";
    public static final String DORIS_SINK_PARALLELISM = "doris.sink.parallelism";
    public static final String DEFAULT_DORIS_TABLE_NAME = "dwd_session_logs";
    
    // Redis连接池配置
    public static final String REDIS_POOL_MAX_WAIT = "redis.pool.max-wait";
    public static final String REDIS_KEY_TTL = "redis.key.ttl";

    // Elasticsearch相关配置
    public static final String ELASTICSEARCH_HOST = "elasticsearch.host";
    public static final String ELASTICSEARCH_PORT = "elasticsearch.port";
    public static final String ELASTICSEARCH_SCHEME = "elasticsearch.scheme";
    public static final String ELASTICSEARCH_USERNAME = "ELASTICSEARCH_USERNAME";
    public static final String ELASTICSEARCH_PASSWORD = "ELASTICSEARCH_PASSWORD";

    // PostgreSQL相关配置
    public static final String POSTGRESQL_HOST = "postgresql.host";
    public static final String POSTGRESQL_PORT = "postgresql.port";
    public static final String POSTGRESQL_DATABASE = "postgresql.database";
    public static final String POSTGRESQL_USERNAME = "POSTGRESQL_USERNAME";
    public static final String POSTGRESQL_PASSWORD = "POSTGRESQL_PASSWORD";
    public static final String POSTGRESQL_URL = "postgresql.url";
    public static final String POSTGRESQL_CONNECTION_TIMEOUT = "postgresql.connection.timeout";
    public static final String POSTGRESQL_MAX_POOL_SIZE = "postgresql.max.pool.size";
    public static final String POSTGRESQL_MIN_IDLE = "postgresql.min.idle";
    public static final String POSTGRESQL_MAX_LIFETIME = "postgresql.max.lifetime";

    // Nebula相关配置
    public static final String NEBULA_META_ADDR = "nebula.meta.addr";
    public static final String NEBULA_GRAPH_ADDR = "nebula.graph.addr";
    public static final String NEBULA_SPACE_NAME = "nebula.space.name";
    public static final String NEBULA_USERNAME = "NEBULA_USERNAME";
    public static final String NEBULA_PASSWORD = "NEBULA_PASSWORD";
    public static final String NEBULA_POOL_MAX_CONN_SIZE = "nebula.pool.max.conn.size";
    public static final String NEBULA_POOL_MIN_CONN_SIZE = "nebula.pool.min.conn.size";
    public static final String NEBULA_POOL_IDLE_TIME = "nebula.pool.idle.time";
    public static final String NEBULA_POOL_TIMEOUT = "nebula.pool.timeout";
    public static final String NEBULA_SESSION_SIZE = "nebula.session.size";
    public static final String NEBULA_BATCH_SIZE = "nebula.batch.size";
    public static final String NEBULA_BATCH_INTERVAL = "nebula.batch.interval";

    // MinIO相关配置
    public static final String MINIO_ENDPOINT = "minio.endpoint";
    public static final String MINIO_ACCESS_KEY = "MINIO_ACCESS_KEY";
    public static final String MINIO_SECRET_KEY = "MINIO_SECRET_KEY";
    public static final String MINIO_BUCKET_NAME = "minio.bucket.name";
    public static final String MINIO_PATH_PREFIX = "minio.path.prefix";

    // 并行度相关配置
    public static final String PARALLELISM_KAFKA_SOURCE = "parallelism.kafka.source";
    public static final String PARALLELISM_PARSING = "parallelism.parsing";
    public static final String PARALLELISM_PROCESSING = "parallelism.processing";
    public static final String PARALLELISM_ELASTICSEARCH_SINK = "parallelism.elasticsearch.sink";
    public static final String PARALLELISM_KAFKA_SINK = "parallelism.kafka.sink";
    public static final String PARALLELISM_KAFKA_JSON_SINK = "parallelism.kafka.json-sink";
    public static final String PARALLELISM_CONNECT_INFO_UPDATE = "parallelism.connect-info.update";
    public static final String PARALLELISM_NEBULA_SINK = "parallelism.nebula.sink";

    // 威胁检测器相关配置
    public static final String THREAT_DETECTOR_ENABLED = "threat.detector.enabled";
    public static final String THREAT_DETECTOR_PARALLELISM = "threat.detector.parallelism";
    public static final String THREAT_DETECTOR_BUFFER_SIZE = "threat.detector.buffer.size";
    public static final String THREAT_DETECTOR_TIMEOUT_MS = "threat.detector.timeout.ms";
    public static final String THREAT_DETECTOR_DEBUG_ENABLED = "threat.detector.debug.enabled";

    // 威胁检测器状态管理配置
    public static final String THREAT_DETECTOR_STATE_TTL_DEFAULT = "threat.detector.state.ttl.default";
    public static final String THREAT_DETECTOR_STATE_TTL_ATTACK_CHAIN = "threat.detector.state.ttl.attack-chain";
    public static final String THREAT_DETECTOR_STATE_TTL_DETECTOR = "threat.detector.state.ttl.detector";

    // 攻击链分析配置
    public static final String ATTACK_CHAIN_ANALYSIS_ENABLED = "attack.chain.analysis.enabled";
    public static final String ATTACK_CHAIN_CORRELATION_WINDOW_MINUTES = "attack.chain.correlation.window.minutes";
    public static final String ATTACK_CHAIN_ATTACH_RESULT = "attack.chain.attach.result";
    public static final String ATTACK_CHAIN_CLEANUP_INTERVAL_HOURS = "attack.chain.cleanup.interval.hours";

    // Flink状态配置
    public static final String FLINK_CHECKPOINT_INTERVAL = "flink.checkpoint.interval";
    public static final String FLINK_CHECKPOINT_MODE = "flink.checkpoint.mode";
    public static final String FLINK_STATE_BACKEND = "flink.state.backend";
    public static final String FLINK_STATE_TTL_DETECTION_CONTEXT = "flink.state.ttl.detection-context";
    public static final String FLINK_STATE_TTL_RECENT_EVENTS = "flink.state.ttl.recent-events";
    public static final String FLINK_STATE_TTL_WINDOW_DATA = "flink.state.ttl.window-data";
}
